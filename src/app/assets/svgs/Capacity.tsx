import * as React from 'react';

const SvgComponent = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width={16}
    height={17}
    viewBox='0 0 16 17'
    fill='none'
    {...props}
  >
    <g
      stroke={props.color ?? '#6D7380'}
      strokeLinecap='round'
      strokeLinejoin='round'
      clipPath='url(#a)'
    >
      <path d='M7.96 9.004a3.76 3.76 0 1 0 0-7.52 3.76 3.76 0 0 0 0 7.52ZM11.58 16.484H4.34v-3.7a2.33 2.33 0 0 1 2.34-2.33l1.22 2 1.35-2a2.33 2.33 0 0 1 2.33 2.33v3.7Z' />
      <path d='M5.6 8.114a3 3 0 0 1-5.13-2.13 3 3 0 0 1 4.17-2.76M4.28 14.984H.58v-3a1.87 1.87 0 0 1 1.86-1.83l1 1.61 1.06-1.61c.357.01.704.121 1 .32M10.4 8.114a3 3 0 0 0 5.13-2.13 3 3 0 0 0-4.17-2.76M11.72 14.984h3.7v-3a1.87 1.87 0 0 0-1.86-1.87l-1 1.61-1.06-1.57c-.374 0-.74.112-1.05.32' />
    </g>
    <defs>
      <clipPath id='a'>
        <path fill='#fff' d='M0 .984h16v16H0z' />
      </clipPath>
    </defs>
  </svg>
);
export default SvgComponent;
