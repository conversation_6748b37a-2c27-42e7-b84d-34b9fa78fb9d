import * as React from 'react';
import { SVGProps } from 'react';

const SvgBathroom = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={16}
    height={17}
    viewBox='0 0 16 17'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
    {...props}
  >
    <g clipPath='url(#clip0_9903_142619)'>
      <path
        d='M3.99967 14.0596V15.3929'
        stroke={props.color ?? '#6D7380'}
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M11.9997 14.0596V15.3929'
        stroke={props.color ?? '#6D7380'}
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M1 9.39258V12.0592C1 13.1638 1.89543 14.0592 3 14.0592H13C14.1046 14.0592 15 13.1638 15 12.0592V9.39258H1Z'
        stroke={props.color ?? '#6D7380'}
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M10 5.39258H6C6.00329 4.28938 6.8968 3.39587 8 3.39258V3.39258C9.1032 3.39587 9.99671 4.28938 10 5.39258V5.39258Z'
        stroke={props.color ?? '#6D7380'}
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M8 3.39274V2.72607C8 1.6215 8.89543 0.726074 10 0.726074V0.726074C11.1046 0.726074 12 1.6215 12 2.72607V7.72607'
        stroke={props.color ?? '#6D7380'}
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <circle
        cx={6.66634}
        cy={6.72591}
        r={0.333333}
        stroke={props.color ?? '#6D7380'}
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <circle
        cx={8.00033}
        cy={7.72591}
        r={0.333333}
        stroke={props.color ?? '#6D7380'}
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <circle
        cx={9.33333}
        cy={6.72591}
        r={0.333333}
        stroke={props.color ?? '#6D7380'}
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </g>
    <defs>
      <clipPath id='clip0_9903_142619'>
        <rect width={16} height={16} fill='white' transform='translate(0 0.0595703)' />
      </clipPath>
    </defs>
  </svg>
);

export default SvgBathroom;
