import * as React from 'react';
import { SVGProps } from 'react';

const SvgBedroom = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={16}
    height={16}
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M0.733871 13.6129V2"
      stroke={props.color ?? '#6D7380'}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M0.733398 11.6772H15.2495"
      stroke={props.color ?? '#6D7380'}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M0.733398 8.77441H15.2495V13.6131"
      stroke={props.color ?? '#6D7380'}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <circle
      cx={4.12056}
      cy={5.38716}
      r={1.45161}
      stroke={props.color ?? '#6D7380'}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M15.2497 6.83877H7.50781V3.93555H13.3143C14.3832 3.93555 15.2497 4.80209 15.2497 5.87103V6.83877Z"
      stroke={props.color ?? '#6D7380'}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export default SvgBedroom;
