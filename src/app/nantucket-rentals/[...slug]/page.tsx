import { ChatBubbleOvalLeftIcon, PhoneIcon } from '@heroicons/react/24/outline';

import ListingHeroImages from '@/app/components/rental-listing-details/ListingHeroImages';
import PropertyDetails from '@/app/components/rental-listing-details/PropertyDetails';
import PropertyFeaturedAmenities from '@/app/components/rental-listing-details/PropertyFeaturedAmenities';
import PropertyHouseRules from '@/app/components/rental-listing-details/PropertyHouseRules';
import PropertySleepingArrangement from '@/app/components/rental-listing-details/PropertySleepingArrangement';
import { generatePropertyRentaltJsonLd } from '@/app/components/rental-listing/PropertyCard';
import { getListingDetailsByNeighborhoodAndSlug } from '@/app/services/rental-listing-details';
import { H1, H2 } from '@/app/ui/Typography';
import { Separator } from '@/app/ui/separator';
import ExpandableText from '@/clients/components/common/ExpandableText';
import InquireButton from '@/clients/components/rental-listing-details/Inquire/InquireButton';
import ListingDetailsWrapper from '@/clients/components/rental-listing-details/ListingDetailsWrapper';
import PropertyCheckout from '@/clients/components/rental-listing-details/PropertyCheckout';
import StickyBookButton from '@/clients/components/rental-listing-details/StickyBookButton';
import { BookingContextProvider } from '@/clients/contexts/BookingContext';
import { IListingDetails } from '@/types/rental-listing-details';
import { isMobileDevice } from '@/utils/responsive';
import { GoogleMapsEmbed } from '@next/third-parties/google';

import { Metadata, ResolvingMetadata } from 'next';
import { headers } from 'next/headers';
import Image from 'next/image';
import { redirect } from 'next/navigation';
import Script from 'next/script';

export const revalidate = 600;

const MAPS_API_KEY = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || '';

type PageProps = {
  params: Promise<{ slug: string | string[] }>;
};

export async function generateMetadata(
  { params }: PageProps,
  parent: ResolvingMetadata,
): Promise<Metadata> {
  const { slug } = await params;
  const formattedSlug = (slug as string[]).join('/') ?? '';

  const listing = await getListingDetailsByNeighborhoodAndSlug<IListingDetails>(formattedSlug);

  const previousImages = (await parent)?.openGraph?.images || [];

  // Handle case when listing is null
  if (!listing) {
    return {
      title: 'Property Not Found | Congdon & Coleman',
      description: 'The requested property could not be found.',
      metadataBase: new URL('https://www.congdonandcoleman.com'),
      robots: 'noindex,nofollow',
      openGraph: {
        images: [...previousImages],
      },
    };
  }

  return {
    title: listing.title_tag ?? '',
    description: listing.meta_description ?? '',
    metadataBase: new URL('https://www.congdonandcoleman.com'),
    alternates: {
      canonical: 'https://www.congdonandcoleman.com/nantucket-rentals/',
    },
    robots: listing.meta_robots ?? 'noindex,nofollow',
    openGraph: {
      images: [...previousImages],
    },
  };
}

export default async function RentalListingDetails({ params }: PageProps) {
  const headersList = await headers();
  const userAgent = headersList.get('user-agent');
  const isMobile = isMobileDevice(userAgent ?? '');
  const { slug } = await params;
  const formattedSlug = (slug as string[]).join('/') ?? '';

  const rentalListingDetails =
    await getListingDetailsByNeighborhoodAndSlug<IListingDetails>(formattedSlug);

  if (!rentalListingDetails) {
    redirect('/nantucket-rentals/');
  }

  const propertyTitle = `${rentalListingDetails.address} | ${rentalListingDetails.area.name}`;

  return (
    <main className=''>
      <div className='bg-[#F0F9FF] border-solid border-0 border-b-[1px] border-b-[#E0F2FE]'>
        <div className='container flex items-center justify-center gap-x-3 py-2.5'>
          <svg
            xmlns='http://www.w3.org/2000/svg'
            width='17'
            height='17'
            viewBox='0 0 17 17'
            fill='none'
            className='hidden lg:block'
          >
            <path
              d='M9.6534 4.5H14.9867'
              stroke='#678993'
              strokeLinecap='round'
              strokeLinejoin='round'
            />
            <path
              d='M12.3201 1.83333L14.9867 4.5L12.3201 7.16667'
              stroke='#678993'
              strokeLinecap='round'
              strokeLinejoin='round'
            />
            <path
              d='M9.5414 11.5453C9.82501 11.6756 10.161 11.5916 10.3501 11.3433L10.5867 11.0333C10.8385 10.6976 11.2337 10.5 11.6534 10.5H13.6534C14.3893 10.5 14.9867 11.0974 14.9867 11.8333V13.8333C14.9867 14.5692 14.3893 15.1667 13.6534 15.1667C7.03042 15.1667 1.6534 9.78965 1.6534 3.16667C1.6534 2.43029 2.25036 1.83333 2.98674 1.83333H4.98673C5.72262 1.83333 6.32007 2.43078 6.32007 3.16667V5.16667C6.32007 5.58634 6.12248 5.98153 5.78674 6.23333L5.47474 6.46733C5.22238 6.66002 5.14094 7.00394 5.28007 7.28933C6.19119 9.13991 7.68968 10.6365 9.5414 11.5453'
              stroke='#678993'
              strokeLinecap='round'
              strokeLinejoin='round'
            />
          </svg>
          <p className='text-sm text-[#334155] m-0 hidden lg:block'>
            Questions or ready to book? Speak to a local specialist now:
          </p>
          <a
            href='tel:************'
            className='flex items-center justify-center px-3 py-[5px] border border-solid border-olive gap-x-2 rounded-full bg-white no-underline text-olive-variant'
          >
            <PhoneIcon className='w-4 h-4' />
            <span className='text-sm font-medium'>************</span>
          </a>
        </div>
      </div>
      <ListingHeroImages
        listingImages={rentalListingDetails?.images ?? []}
        propertyHeadline={propertyTitle}
        isMobile={isMobile}
      />
      <div className='container xl:p-0'>
        <BookingContextProvider
          nrPropertyId={rentalListingDetails.listing_id}
          property={rentalListingDetails}
        >
          <ListingDetailsWrapper details={rentalListingDetails}>
            <div className='flex gap-x-6 pt-4 pb-[130px] md:pb-20'>
              <div className='w-full md:flex-grow md:w-[calc(100%-480px)]'>
                <H1 className='font-medium m-0 leading-normal mb-2'>{propertyTitle}</H1>
                <Separator className='my-2 md:my-3' />
                <div className='flex flex-col-reverse md:flex-col'>
                  <div className='flex items-center justify-center space-x-4 mb-4 md:mb-0 md:mt-5'>
                    <Image
                      alt='NR logo'
                      src='/images/nr-logo.svg'
                      width={0}
                      height={0}
                      className='h-[33px] md:h-[50px] w-auto'
                    />
                    <span className='text-xl'>X</span>
                    <Image
                      alt='CNC logo'
                      src='/images/cc-logo.svg'
                      width={0}
                      height={0}
                      className='h-[33px] md:h-[50px] w-auto'
                    />
                  </div>
                  <PropertyDetails details={rentalListingDetails} />
                </div>
                <div className='px-4 py-3 bg-light-slate border-solid border-[#BAE6FD] my-4 rounded-xl flex items-center justify-between flex-col md:flex-row'>
                  <div className='flex items-center gap-x-3 max-w-full md:max-w-[70%] my-4 md:my-0'>
                    <PhoneIcon className='h-5 w-5 text-olive-variant hidden md:block' />
                    <span className='text-sm text-[#475569]'>
                      Got questions about dates, rates, or policies?
                    </span>
                  </div>
                  <div className='flex items-center justify-between md:justify-end w-full md:w-[30%]'>
                    <a
                      href='tel:************'
                      className='md:hidden flex items-center justify-center px-3 py-[5px] gap-x-2 rounded-full bg-olive-variant shadow-sm no-underline text-white font-medium w-[45%] md:w-[146px]'
                    >
                      <PhoneIcon className='h-5 w-5' />
                      Call Now
                    </a>

                    <InquireButton
                      listingDetails={rentalListingDetails}
                      title='Inquire'
                      className='w-[45%] md:w-[146px] px-3 py-[5px] !border !border-olive-variant !border-solid rounded-full font-medium text-black hover:text-black/70 !block text-sm md:text-base'
                    />
                  </div>
                </div>
                <Separator className='my-2 md:my-3' />
                <PropertySleepingArrangement bedrooms={rentalListingDetails.bedrooms} />
                <PropertyFeaturedAmenities details={rentalListingDetails} />
                <Separator className='my-4 md:my-8' />
                <ExpandableText
                  className='text-sm md:text-base text-metal-gray m-0'
                  text={rentalListingDetails.description}
                />
                <Separator className='my-4 md:my-8' />
                <H2 className='m-0 mb-4 md:mb-6 flex items-center gap-x-2'>
                  <svg
                    xmlns='http://www.w3.org/2000/svg'
                    width='36'
                    height='37'
                    viewBox='0 0 36 37'
                    fill='none'
                  >
                    <path
                      fillRule='evenodd'
                      clipRule='evenodd'
                      d='M6.375 13.2207C6.375 7.12769 11.6572 2.32373 18 2.32373C24.3428 2.32373 29.625 7.12769 29.625 13.2207C29.625 18.9374 26.0807 25.6488 20.3622 28.0926C18.8609 28.7341 17.1391 28.7341 15.6378 28.0926C9.91933 25.6488 6.375 18.9374 6.375 13.2207ZM18 4.57373C12.7448 4.57373 8.625 8.51987 8.625 13.2207C8.625 18.2132 11.7831 23.9984 16.522 26.0236C17.4585 26.4238 18.5415 26.4238 19.478 26.0236C24.2169 23.9984 27.375 18.2132 27.375 13.2207C27.375 8.51987 23.2552 4.57373 18 4.57373ZM18 12.0737C16.9645 12.0737 16.125 12.9132 16.125 13.9487C16.125 14.9843 16.9645 15.8237 18 15.8237C19.0355 15.8237 19.875 14.9843 19.875 13.9487C19.875 12.9132 19.0355 12.0737 18 12.0737ZM13.875 13.9487C13.875 11.6706 15.7218 9.82373 18 9.82373C20.2782 9.82373 22.125 11.6706 22.125 13.9487C22.125 16.2269 20.2782 18.0737 18 18.0737C15.7218 18.0737 13.875 16.2269 13.875 13.9487ZM5.39312 22.9436C5.81016 23.4042 5.77488 24.1156 5.31432 24.5326C4.46278 25.3037 4.125 26.0418 4.125 26.6987C4.125 27.8442 5.21101 29.256 7.85506 30.4458C10.3935 31.5881 13.9793 32.3237 18 32.3237C22.0207 32.3237 25.6065 31.5881 28.1449 30.4458C30.789 29.256 31.875 27.8442 31.875 26.6987C31.875 26.0418 31.5372 25.3037 30.6857 24.5326C30.2251 24.1156 30.1898 23.4042 30.6069 22.9436C31.0239 22.4831 31.7354 22.4478 32.1959 22.8648C33.3404 23.9012 34.125 25.2004 34.125 26.6987C34.125 29.2811 31.8531 31.2444 29.0683 32.4976C26.1778 33.7983 22.2635 34.5737 18 34.5737C13.7365 34.5737 9.82223 33.7983 6.93174 32.4976C4.14685 31.2444 1.875 29.2811 1.875 26.6987C1.875 25.2004 2.65957 23.9012 3.80408 22.8648C4.26464 22.4478 4.97608 22.4831 5.39312 22.9436Z'
                      fill='#1184B7'
                    />
                  </svg>
                  Explore the Neighborhood
                </H2>

                <div className='rounded-lg overflow-hidden'>
                  <GoogleMapsEmbed
                    apiKey={MAPS_API_KEY}
                    mode='place'
                    zoom='16'
                    height={340}
                    width='100%'
                    q={`${rentalListingDetails.latitude ?? 41.2835},${
                      rentalListingDetails.longitude ?? -70.0995
                    }`}
                  />
                </div>
                <div className='my-4 md:my-8 border border-solid border-platinium px-6 py-4 rounded-lg'>
                  <div className='flex items-start justify-between mb-4'>
                    <p className='flex items-start gap-x-4 text-[#696F8C] max-w-[70%] text-sm md:text-base'>
                      <svg
                        xmlns='http://www.w3.org/2000/svg'
                        width='24'
                        height='24'
                        viewBox='0 0 36 37'
                        fill='none'
                      >
                        <path
                          fillRule='evenodd'
                          clipRule='evenodd'
                          d='M6.375 13.2207C6.375 7.12769 11.6572 2.32373 18 2.32373C24.3428 2.32373 29.625 7.12769 29.625 13.2207C29.625 18.9374 26.0807 25.6488 20.3622 28.0926C18.8609 28.7341 17.1391 28.7341 15.6378 28.0926C9.91933 25.6488 6.375 18.9374 6.375 13.2207ZM18 4.57373C12.7448 4.57373 8.625 8.51987 8.625 13.2207C8.625 18.2132 11.7831 23.9984 16.522 26.0236C17.4585 26.4238 18.5415 26.4238 19.478 26.0236C24.2169 23.9984 27.375 18.2132 27.375 13.2207C27.375 8.51987 23.2552 4.57373 18 4.57373ZM18 12.0737C16.9645 12.0737 16.125 12.9132 16.125 13.9487C16.125 14.9843 16.9645 15.8237 18 15.8237C19.0355 15.8237 19.875 14.9843 19.875 13.9487C19.875 12.9132 19.0355 12.0737 18 12.0737ZM13.875 13.9487C13.875 11.6706 15.7218 9.82373 18 9.82373C20.2782 9.82373 22.125 11.6706 22.125 13.9487C22.125 16.2269 20.2782 18.0737 18 18.0737C15.7218 18.0737 13.875 16.2269 13.875 13.9487ZM5.39312 22.9436C5.81016 23.4042 5.77488 24.1156 5.31432 24.5326C4.46278 25.3037 4.125 26.0418 4.125 26.6987C4.125 27.8442 5.21101 29.256 7.85506 30.4458C10.3935 31.5881 13.9793 32.3237 18 32.3237C22.0207 32.3237 25.6065 31.5881 28.1449 30.4458C30.789 29.256 31.875 27.8442 31.875 26.6987C31.875 26.0418 31.5372 25.3037 30.6857 24.5326C30.2251 24.1156 30.1898 23.4042 30.6069 22.9436C31.0239 22.4831 31.7354 22.4478 32.1959 22.8648C33.3404 23.9012 34.125 25.2004 34.125 26.6987C34.125 29.2811 31.8531 31.2444 29.0683 32.4976C26.1778 33.7983 22.2635 34.5737 18 34.5737C13.7365 34.5737 9.82223 33.7983 6.93174 32.4976C4.14685 31.2444 1.875 29.2811 1.875 26.6987C1.875 25.2004 2.65957 23.9012 3.80408 22.8648C4.26464 22.4478 4.97608 22.4831 5.39312 22.9436Z'
                          fill='#1184B7'
                        />
                      </svg>
                      Nearest Beach
                    </p>
                    <p className='text-olive text-sm md:text-base'>
                      {Number(rentalListingDetails?.distance_to_beach ?? 0)} miles
                    </p>
                  </div>
                  <div className='flex items-start justify-between mb-4'>
                    <p className='flex items-start gap-x-4 text-[#696F8C] max-w-[70%] text-sm md:text-base'>
                      <svg
                        xmlns='http://www.w3.org/2000/svg'
                        width='24'
                        height='24'
                        viewBox='0 0 36 37'
                        fill='none'
                      >
                        <path
                          fillRule='evenodd'
                          clipRule='evenodd'
                          d='M6.375 13.2207C6.375 7.12769 11.6572 2.32373 18 2.32373C24.3428 2.32373 29.625 7.12769 29.625 13.2207C29.625 18.9374 26.0807 25.6488 20.3622 28.0926C18.8609 28.7341 17.1391 28.7341 15.6378 28.0926C9.91933 25.6488 6.375 18.9374 6.375 13.2207ZM18 4.57373C12.7448 4.57373 8.625 8.51987 8.625 13.2207C8.625 18.2132 11.7831 23.9984 16.522 26.0236C17.4585 26.4238 18.5415 26.4238 19.478 26.0236C24.2169 23.9984 27.375 18.2132 27.375 13.2207C27.375 8.51987 23.2552 4.57373 18 4.57373ZM18 12.0737C16.9645 12.0737 16.125 12.9132 16.125 13.9487C16.125 14.9843 16.9645 15.8237 18 15.8237C19.0355 15.8237 19.875 14.9843 19.875 13.9487C19.875 12.9132 19.0355 12.0737 18 12.0737ZM13.875 13.9487C13.875 11.6706 15.7218 9.82373 18 9.82373C20.2782 9.82373 22.125 11.6706 22.125 13.9487C22.125 16.2269 20.2782 18.0737 18 18.0737C15.7218 18.0737 13.875 16.2269 13.875 13.9487ZM5.39312 22.9436C5.81016 23.4042 5.77488 24.1156 5.31432 24.5326C4.46278 25.3037 4.125 26.0418 4.125 26.6987C4.125 27.8442 5.21101 29.256 7.85506 30.4458C10.3935 31.5881 13.9793 32.3237 18 32.3237C22.0207 32.3237 25.6065 31.5881 28.1449 30.4458C30.789 29.256 31.875 27.8442 31.875 26.6987C31.875 26.0418 31.5372 25.3037 30.6857 24.5326C30.2251 24.1156 30.1898 23.4042 30.6069 22.9436C31.0239 22.4831 31.7354 22.4478 32.1959 22.8648C33.3404 23.9012 34.125 25.2004 34.125 26.6987C34.125 29.2811 31.8531 31.2444 29.0683 32.4976C26.1778 33.7983 22.2635 34.5737 18 34.5737C13.7365 34.5737 9.82223 33.7983 6.93174 32.4976C4.14685 31.2444 1.875 29.2811 1.875 26.6987C1.875 25.2004 2.65957 23.9012 3.80408 22.8648C4.26464 22.4478 4.97608 22.4831 5.39312 22.9436Z'
                          fill='#1184B7'
                        />
                      </svg>
                      Distance to Hub
                    </p>
                    <p className='text-olive text-sm md:text-base'>
                      {Number(rentalListingDetails?.distance_to_the_hub ?? 0)} miles
                    </p>
                  </div>
                </div>
                <PropertyHouseRules details={rentalListingDetails} />
              </div>
              <div className='hidden md:block w-full md:w-[480px]'>
                <PropertyCheckout
                  details={rentalListingDetails}
                  isMobile={isMobile}
                  petsAllowed={
                    rentalListingDetails?.requirement?.pet_allow?.toLowerCase() === 'true'
                  }
                />
              </div>
            </div>
            <StickyBookButton
              details={rentalListingDetails}
              petsAllowed={rentalListingDetails?.requirement?.pet_allow?.toLowerCase() === 'true'}
            />
          </ListingDetailsWrapper>
        </BookingContextProvider>
      </div>
      <Script
        id={`rental-${rentalListingDetails.slug.split(' ').join().toLocaleLowerCase()}`}
        type='application/ld+json'
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(generatePropertyRentaltJsonLd(rentalListingDetails)),
        }}
      />
    </main>
  );
}
