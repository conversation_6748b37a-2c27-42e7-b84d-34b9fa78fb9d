import * as React from 'react';

import { twMerge } from 'tailwind-merge';

interface SeparatorProps extends React.HTMLAttributes<HTMLDivElement> {
  /**
   * The orientation of the separator.
   * @default 'horizontal'
   */
  orientation?: 'horizontal' | 'vertical';

  /**
   * When true, indicates that the separator is purely visual and does not have semantic meaning.
   * @default true
   */
  decorative?: boolean;
}

const Separator = React.forwardRef<HTMLDivElement, SeparatorProps>(
  ({ className, orientation = 'horizontal', decorative = true, ...props }, ref) => {
    const ariaProps = decorative
      ? { 'aria-hidden': true }
      : { role: 'separator', 'aria-orientation': orientation };

    return (
      <div
        ref={ref}
        className={twMerge(
          'shrink-0 bg-disabled',
          orientation === 'horizontal' ? 'h-[1px] w-full' : 'h-full w-[1px]',
          className,
        )}
        {...ariaProps}
        {...props}
      />
    );
  },
);

Separator.displayName = 'Separator';

export { Separator };
