import { StarIcon } from '@heroicons/react/20/solid';

import { getPropertyTypeLabel } from '@/app/utils';
import { Icon } from '@/clients/components/icon';
import PropertyCardClickableLink from '@/clients/components/rental-listings/PropertyCardLink';
import { roundInteger } from '@/utils/common';

import Image from 'next/image';
import Script from 'next/script';

export type RentalPropertyProps = {
  listing_id: number;
  slug: string;
  area_name: string;
  peak_price?: number;
  max_price: number;
  min_price: number;
  small_url: string;
  url: string;
  address: string;
  bedroom_number: number;
  bathroom_number: number;
  avg_rating?: number;
  ratings_count?: number;
  capacity: number;
  owner_email?: string;
  virtual_tour_link?: string;
};
type PropertyCardProps = {
  property: RentalPropertyProps;
  preload?: boolean;
  className?: string;
  sortByPrice?: boolean;
};

export const generatePropertyRentaltJsonLd = (property: RentalPropertyProps) => {
  const rating = property.avg_rating
    ? {
        '@type': 'AggregateRating',
        ratingValue: property.avg_rating,
        reviewCount: property.ratings_count,
      }
    : {};

  return {
    '@context': 'https://schema.org',
    '@type': ['Product', 'Accommodation'],
    name: property.address,
    url: `https://www.congdonandcoleman.com/nantucket-rentals/${property.slug}`,
    maximumAttendeeCapacity: property.capacity,
    numberOfBathroomsTotal: property.bathroom_number,
    numberOfBedrooms: property.bedroom_number,
    address: {
      '@type': 'PostalAddress',
      addressCountry: 'US',
      addressLocality: 'Nantucket',
      addressRegion: 'Massachusetts',
      postalCode: '02554',
      streetAddress: property.address,
    },
    offers: {
      '@type': 'AggregateOffer',
      highPrice: property.max_price,
      lowPrice: property.min_price,
      priceCurrency: 'USD',
    },
    image: property.url,
    ...rating,
  };
};

export const PropertyCard = ({
  property,
  preload = false,
  className = '',
  sortByPrice = false,
}: PropertyCardProps) => {
  const weeklyPrice = new Intl.NumberFormat('en-US').format(
    sortByPrice ? property.peak_price ?? property.max_price : property.max_price,
  );
  const analyticsData = {
    name: property.address,
    product_id: property.listing_id,
    price: (property?.max_price ?? 0).toFixed(2),
    quantity: '1',
    category: property.area_name,
    image_url: {
      url: property.url,
      small_url: property.small_url,
    },
  };
  const townZip = 'Nantucket MA, 02554';
  const propertyTypeLabel = 'Vacation Rental';

  return (
    <>
      <div className={`relative shadow-md rounded-b ${className}`}>
        <div className='relative mb-4 overflow-hidden h-52 md:h-44'>
          <PropertyCardClickableLink
            analyticsData={analyticsData}
            href={`/nantucket-rentals/${property.slug}`}
          >
            <Image
              src={property?.small_url ?? property?.url ?? 'https://placehold.co/150'}
              sizes='100vw'
              className='object-cover hover:scale-105'
              style={{
                width: '100%',
                height: '100%',
                transition: 'all .6s',
              }}
              width={265}
              height={177}
              alt={`rental property in ${property.address}`}
              placeholder='blur'
              blurDataURL='https://placehold.co/150'
              priority={preload}
            />
          </PropertyCardClickableLink>
        </div>
        <div className='absolute top-2 left-2 flex'>
          <div className='bg-white px-3 py-1 rounded text-black'>{property.area_name}</div>
          {property?.virtual_tour_link && (
            <div className='ml-3 bg-primary-slate px-3 py-1 rounded text-white'>3D Tour</div>
          )}
        </div>
        <div className='px-2.5 pb-4 rounded-b'>
          <div className='pb-2'>
            <div className='flex gap-x-1 items-center'>
              <span className='h-2.5 w-2.5 rounded-full bg-[#008000] inline-block animate-pulse'></span>
              <small className='text-black'>{propertyTypeLabel}</small>
            </div>
            <PropertyCardClickableLink
              analyticsData={analyticsData}
              aria-label={`Details for vacation rental ${property.address}`}
              href={`/nantucket-rentals/${property.slug}`}
            >
              <h2 className='font-medium text-[#09182C]'>{property.address}</h2>
            </PropertyCardClickableLink>
            <small className='text-black'>{townZip}</small>
          </div>
          <div className='pb-2 text-gray-500 flex justify-between'>
            <div className='flex justify-between w-full'>
              {property.bedroom_number ? (
                <div className='flex gap-x-2'>
                  {property.bedroom_number}
                  <Icon height={24} width={17} icon='bed' alt={`${property.bedroom_number} beds`} />
                </div>
              ) : null}
              {property.bathroom_number ? (
                <div className='flex gap-x-2'>
                  {property.bathroom_number}
                  <Icon
                    height={24}
                    width={17}
                    icon='shower'
                    alt={`${property.bathroom_number} bathrooms`}
                  />
                </div>
              ) : null}
              {property?.capacity ? <div>Sleeps {property.capacity}</div> : null}
              {property?.avg_rating && (
                <div className='flex gap-x-2 items-center'>
                  {roundInteger(property.avg_rating)}{' '}
                  <StarIcon color='rgb(109, 115, 128)' className='w-4 h-4' />
                </div>
              )}
            </div>
          </div>
          <hr className='pb-2' />
          <div className='flex justify-between pb-2'>
            <div className='text-blue-950'>Up to ${weeklyPrice}/Wk</div>
          </div>
        </div>
      </div>
      <Script
        id={`rental-${property.slug.split(' ').join().toLocaleLowerCase()}`}
        type='application/ld+json'
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(generatePropertyRentaltJsonLd(property)),
        }}
      />
    </>
  );
};
