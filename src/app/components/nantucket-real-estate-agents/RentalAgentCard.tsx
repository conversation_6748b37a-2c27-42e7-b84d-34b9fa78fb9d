import { IAgent } from '@/types/agents';

import Image from 'next/image';

type Props = {
  agent: IAgent;
};

const RentalAgentCard = ({ agent }: Props) => {
  return (
    <div className='p-2.5 border rounded shadow'>
      <div className='w-full h-[198px] md:h-[175px] relative'>
        {agent.avatar && (
          <Image
            alt='Agent avatar'
            fill
            className='rounded hover:scale-105 transition-all object-cover'
            src={agent.avatar}
            placeholder='blur'
            sizes='(max-width: 600px) 160px, 
              (max-width: 768px) 40vw,
              30vw'
            blurDataURL='https://placehold.co/150'
          />
        )}
      </div>
      <div className='px-2.5'>
        <p className='text-lg font-medium mt-5 leading-[28px]'>{`${agent?.first_name} ${agent?.last_name}`}</p>
        <p className='text-sm leading-[26px] text-dark-3'>{agent?.title ?? ''}</p>
        <p className='text-sm leading-[26px] text-dark-3'>M {agent?.phone ?? ''}</p>
      </div>
    </div>
  );
};

export default RentalAgentCard;
