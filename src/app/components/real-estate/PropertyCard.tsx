import { MlsStatus } from '@/app/types';
import { getPropertyTypeLabel, getUnavailablePropertyTypeLabel } from '@/app/utils';
import { Icon } from '@/clients/components/icon';
import PropertyCardLink from '@/clients/components/rental-listings/PropertyCardLink';

import classNames from 'classnames';
import Image from 'next/image';
import Script from 'next/script';

export type RealEstatePropertyProps = {
  MLSAreaMajor: string;
  ListPrice: number;
  link_images?: {
    alt_text?: string | null;
    small_url?: string;
    url?: string;
  }[];
  Address: string;
  BedroomsTotal: number;
  BathroomsTotalDecimal: number;
  StreetNumber: string;
  StreetName: string;
  PublicRemarks: string;
  PropertyType: string;
  Slug: string;
  ListAgentFullName: string;
  LINK_descr: string;
  MetaCanonical: string;
  MlsStatus: MlsStatus;
  TitleTag: string;
  MetaDescription: string;
  OnMarketDate: string;
  link_id: number;
};

type PropertyCardProps = {
  property: RealEstatePropertyProps;
  preload?: boolean;
  className?: string;
};

const generatePropertyRealEstateJsonLd = (property: RealEstatePropertyProps) => {
  const images = [];
  property?.link_images?.forEach((x) => {
    images.push(x?.url ?? '');
  });
  return {
    '@context': 'https://schema.org',
    '@type': ['Product', 'RealEstateListing'],
    image: property?.link_images?.[0]?.url ?? '',
    description: property.LINK_descr,
    url: property.MetaCanonical,
    headline: property.TitleTag,
    name: property.TitleTag,
    datePosted: property.OnMarketDate,
    leaseLength: {
      '@type': 'QuantitativeValue',
      value: property.ListPrice,
      valueReference: 'PropertyValue',
    },
    review: {
      '@type': 'Review',
      reviewRating: {
        '@type': 'Rating',
        ratingValue: 5,
        bestRating: 5,
      },
      author: {
        '@type': 'Person',
        name: property.ListAgentFullName,
      },
    },
    aggregateRating: {
      '@type': 'AggregateRating',
      ratingValue: 5,
      reviewCount: 1,
    },
    offer: {
      '@type': 'Offer',
      price: property.ListPrice,
      priceCurrency: 'USD',
    },
  };
};

export const PropertyCard = ({ property, preload = false, className = '' }: PropertyCardProps) => {
  const ListPrice = new Intl.NumberFormat('en-US').format(property.ListPrice);
  const Address = property.StreetNumber + ' ' + property.StreetName;
  const imageURL = property?.link_images?.[0]?.small_url ?? property?.link_images?.[0]?.url;
  const analyticsData = {
    name: Address,
    product_id: property.link_id,
    price: (property?.ListPrice ?? 0).toFixed(2),
    quantity: '1',
    category: property.MLSAreaMajor,
    image_url: {
      alt_text: property?.link_images?.[0]?.alt_text,
      url: property?.link_images?.[0]?.url,
      small_url: property?.link_images?.[0]?.small_url,
    },
  };

  const unavailableProperty = property.MlsStatus === 'U' || property.MlsStatus === 'S';

  const badgeColor =
    property.MlsStatus === 'U'
      ? 'bg-[#ffa500]'
      : property.MlsStatus === 'S'
      ? 'bg-[#ff0000]'
      : 'bg-[#008000]';

  const propertyTypeLabel = unavailableProperty
    ? getUnavailablePropertyTypeLabel(property.MlsStatus)
    : getPropertyTypeLabel(property.PropertyType);

  return (
    <>
      <div className={`relative shadow-md rounded-b ${className}`}>
        <div className='relative mb-4 overflow-hidden h-52 md:h-44'>
          {imageURL && (
            <Image
              src={imageURL ?? 'https://placehold.co/150'}
              sizes='100vw'
              className='object-cover hover:scale-105'
              style={{
                width: '100%',
                height: '100%',
                transition: 'all .6s',
              }}
              width={265}
              height={177}
              alt={`rental property in ${Address}`}
              placeholder='blur'
              blurDataURL='https://placehold.co/150'
              priority={preload}
            />
          )}
        </div>
        <div className='absolute bg-white px-3 py-1 top-2 left-2 rounded'>
          {property.MLSAreaMajor}
        </div>
        <div className='px-2.5 pb-4 rounded-b'>
          <div className='pb-2'>
            <div className='flex gap-x-1 items-center'>
              <span
                className={classNames(
                  'h-2.5 w-2.5 rounded-full inline-block animate-pulse',
                  badgeColor,
                )}
              ></span>
              <small>{propertyTypeLabel}</small>
            </div>
            <PropertyCardLink
              aria-label={`Details for ${Address}`}
              analyticsData={analyticsData}
              href={`/nantucket-real-estate/${property.Slug}`}
              className='flex'
            >
              <h2 className='font-semibold text-lg text-[#09182C] truncate'>{Address}</h2>
            </PropertyCardLink>
            <small>Nantucket MA, 02554</small>
          </div>
          <div className='mb-2 text-sm text-[#09182C] line-clamp-3'>{property.PublicRemarks}</div>
          <div className='pb-2 text-gray-500 flex justify-between'>
            <div className='flex gap-x-2 h-6'>
              {property.BedroomsTotal ? (
                <span className='flex gap-x-2 mr-4'>
                  <Icon height={24} width={17} icon='bed' alt={`${property.BedroomsTotal} beds`} />
                  {property.BedroomsTotal} Beds
                </span>
              ) : null}
              {property.BathroomsTotalDecimal ? (
                <span className='flex gap-x-2 mr-4'>
                  <Icon
                    height={24}
                    width={17}
                    icon='shower'
                    alt={`${property.BathroomsTotalDecimal} bathrooms`}
                  />
                  {property.BathroomsTotalDecimal} Baths
                </span>
              ) : null}
            </div>
          </div>
          <hr className='pb-2' />
          <div className='flex justify-between items-center'>
            <div className='text-blue-950 font-semibold pr-4 text-lg'>${ListPrice}</div>
            <div>
              <PropertyCardLink
                analyticsData={analyticsData}
                href={`/nantucket-real-estate/${property.Slug}`}
                className='flex'
              >
                <button className='text-sm text-gray-700 bg-transparent'>View Details</button>
                <Icon height={24} width={17} icon='view-details' alt='View details icon' />
              </PropertyCardLink>
            </div>
          </div>
        </div>
      </div>
      <Script
        type='application/ld+json'
        id={`real-estate-${property.Slug.split(' ').join().toLocaleLowerCase()}`}
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(generatePropertyRealEstateJsonLd(property)),
        }}
      />
    </>
  );
};
