import { CheckIcon } from '@heroicons/react/24/outline';

import { getIconSrcPath, IconIdentifier } from '@/utils/icon';

import classNames from 'classnames';
import Image from 'next/image';

type Props = {
  title: string;
  iconIdentifier?: IconIdentifier;
  isAvailable?: boolean;
  hideCheck?: boolean;
  iconPath?: string;
  quantity?: number;
  type?: string;
  className?: string;
};

const AmenityItem = ({
  title,
  iconIdentifier,
  isAvailable,
  hideCheck,
  iconPath,
  quantity,
  type,
}: Props) => {
  return (
    <div className='flex items-center justify-between h-[50px]'>
      <div className='flex items-center gap-x-2 md:gap-x-3'>
        <Image
          src={iconPath ? iconPath : getIconSrcPath(iconIdentifier)}
          alt='amenity icon'
          width={0}
          height={0}
          loading='lazy'
          quality={50}
          className='w-6 h-auto'
        />
        <span className='truncate'>
          {title} {(quantity ?? 0) > 0 && `(${quantity})`} {type && `(${type})`}
        </span>
      </div>
      {!hideCheck && (
        <div
          className={classNames('w-6 h-6  rounded-full flex items-center justify-center', {
            'bg-olive': isAvailable,
            'border border-solid border-platinium': !isAvailable,
          })}
        >
          {isAvailable && <CheckIcon className='w-4 h-4 text-white' />}
        </div>
      )}
    </div>
  );
};
export default AmenityItem;
