import React from 'react';

import Capacity from '@/app/assets/svgs/Capacity';
import { H2 } from '@/app/ui/Typography';
import { IListingDetails } from '@/types/rental-listing-details';

type Props = {
  details: IListingDetails;
};

const convertTo12hours = (timeString: string) =>
  new Date('1970-01-01T' + timeString + 'Z').toLocaleTimeString('en-US', {
    timeZone: 'UTC',
    hour12: true,
    hour: 'numeric',
    minute: 'numeric',
  });

const PropertyHouseRules = ({ details }: Props) => {
  return (
    <>
      <H2 className='m-0 mb-4 md:mb-6'>House Rules</H2>
      <div className='p-6 rounded-2xl border border-solid border-platinium'>
        <div className='flex items-start gap-x-4 mb-4'>
          <div className='w-4 h-auto'>
            <Capacity width={24} height={25} color='#4C737F' />
          </div>
          <div className='text-sm md:text-base text-metal-gray'>
            Max. Overnight Guests - {details?.capacity} people
          </div>
        </div>
        <div className='flex items-start gap-x-4 mb-4'>
          <div className='w-4 h-auto'>
            <svg
              xmlns='http://www.w3.org/2000/svg'
              width='24'
              height='25'
              viewBox='0 0 24 25'
              fill='none'
            >
              <path
                fillRule='evenodd'
                clipRule='evenodd'
                d='M12 3.34814C6.89137 3.34814 2.75 7.48951 2.75 12.5981C2.75 17.7068 6.89137 21.8481 12 21.8481C17.1086 21.8481 21.25 17.7068 21.25 12.5981C21.25 7.48951 17.1086 3.34814 12 3.34814ZM1.25 12.5981C1.25 6.66108 6.06294 1.84814 12 1.84814C17.9371 1.84814 22.75 6.66108 22.75 12.5981C22.75 18.5352 17.9371 23.3481 12 23.3481C6.06294 23.3481 1.25 18.5352 1.25 12.5981ZM12 7.84814C12.4142 7.84814 12.75 8.18393 12.75 8.59814V12.2875L15.0303 14.5678C15.3232 14.8607 15.3232 15.3356 15.0303 15.6285C14.7374 15.9214 14.2626 15.9214 13.9697 15.6285L11.4697 13.1285C11.329 12.9878 11.25 12.7971 11.25 12.5981V8.59814C11.25 8.18393 11.5858 7.84814 12 7.84814Z'
                fill='#4C737F'
              />
            </svg>
          </div>
          <div className='text-sm md:text-base text-metal-gray'>
            Check-in:{' '}
            {details?.requirement?.checkin_time
              ? convertTo12hours(details.requirement.checkin_time)
              : 'N/A'}
            , Check-out:{' '}
            {details?.requirement?.checkout_time
              ? convertTo12hours(details.requirement.checkout_time)
              : 'N/A'}
          </div>
        </div>
        <div className='flex items-start gap-x-4'>
          <div className='w-4 h-auto'>
            <svg
              xmlns='http://www.w3.org/2000/svg'
              width='24'
              height='25'
              viewBox='0 0 24 25'
              fill='none'
            >
              <g clipPath='url(#clip0_23260_4482)'>
                <path
                  d='M16.0492 13.3177C14.9692 11.6329 13.1299 10.627 11.129 10.627C9.12817 10.627 7.28869 11.6329 6.20892 13.3177L3.60663 17.3773C3.17486 18.051 2.97565 18.829 3.03039 19.6271C3.08514 20.4255 3.38891 21.1689 3.90875 21.7773C4.42895 22.3856 5.11633 22.8013 5.89654 22.9796C6.67676 23.1578 7.47656 23.0818 8.20935 22.7597L8.25714 22.7385C10.1012 21.9436 12.2089 21.9507 14.0484 22.7597C14.5228 22.9683 15.0255 23.0737 15.5327 23.0737C15.8084 23.0737 16.0858 23.0424 16.3607 22.9798C17.1409 22.8016 17.8282 22.386 18.3486 21.7777C18.8687 21.1694 19.1726 20.4258 19.2275 19.6275C19.2825 18.829 19.0833 18.051 18.6515 17.3771L16.0492 13.3177ZM17.2795 20.8633C16.6176 21.6371 15.5468 21.8817 14.6147 21.4719C13.507 20.9847 12.3179 20.7411 11.1284 20.7411C9.94006 20.7411 8.75116 20.9845 7.6441 21.4712L7.61261 21.4851C6.68774 21.8762 5.63269 21.6285 4.97809 20.8633C4.31689 20.0893 4.24164 18.9936 4.79095 18.1365L7.39343 14.0768C8.21338 12.7977 9.60974 12.0339 11.129 12.0339C12.6482 12.0339 14.0447 12.7977 14.8649 14.0768L17.467 18.1363C18.0165 18.9938 17.941 20.0897 17.2795 20.8633Z'
                  fill='#4C737F'
                />
                <path
                  d='M4.30755 13.3125C5.08172 13.0151 5.66985 12.3913 5.96373 11.5558C6.24315 10.7607 6.22027 9.86315 5.89892 9.02818C5.57738 8.19377 4.99254 7.51262 4.25225 7.10997C3.47423 6.68718 2.61968 6.61833 1.84679 6.91625C0.291859 7.51409 -0.421703 9.43559 0.256337 11.2005C0.797963 12.6053 2.06121 13.4921 3.33837 13.4921C3.66448 13.4921 3.99151 13.4343 4.30755 13.3125ZM1.5692 10.6952C1.16967 9.65514 1.52068 8.54881 2.35216 8.22911C2.50231 8.17125 2.66106 8.1425 2.82311 8.1425C3.07323 8.1425 3.33159 8.2108 3.58025 8.34612C4.02739 8.5891 4.38463 9.01097 4.58605 9.53374C4.78728 10.0569 4.80523 10.6091 4.6364 11.089C4.48168 11.5289 4.1856 11.8522 3.80291 11.9993L3.80217 11.9996C2.97179 12.3197 1.97002 11.7343 1.5692 10.6952Z'
                  fill='#4C737F'
                />
                <path
                  d='M9.35687 10.1319C11.3007 10.1319 12.8822 8.33014 12.8822 6.11548C12.8822 3.90027 11.3007 2.09814 9.35687 2.09814C7.41321 2.09814 5.83191 3.90027 5.83191 6.11548C5.83191 8.33014 7.41321 10.1319 9.35687 10.1319ZM9.35687 3.50494C10.5249 3.50494 11.4754 4.67609 11.4754 6.11548C11.4754 7.55432 10.5249 8.7251 9.35687 8.7251C8.18884 8.7251 7.23871 7.55432 7.23871 6.11548C7.23871 4.67609 8.18884 3.50494 9.35687 3.50494Z'
                  fill='#4C737F'
                />
                <path
                  d='M15.4451 11.1185H15.4453C15.7443 11.2177 16.0494 11.2651 16.3532 11.2651C17.7708 11.2651 19.1523 10.2333 19.6795 8.64764C19.983 7.73486 19.9631 6.76971 19.6232 5.93017C19.2676 5.05145 18.6087 4.41351 17.7675 4.13373C16.9263 3.85431 16.0161 3.97076 15.2051 4.46185C14.4304 4.93115 13.8367 5.69214 13.5337 6.60492C12.8937 8.53046 13.7512 10.5552 15.4451 11.1185ZM14.8687 7.04858C15.0667 6.45257 15.445 5.96149 15.934 5.66522C16.3865 5.39111 16.8801 5.32135 17.3238 5.46875C17.7673 5.61633 18.1207 5.96753 18.3192 6.45788C18.5336 6.98779 18.5427 7.60779 18.3444 8.20379C17.9491 9.39325 16.8475 10.1019 15.889 9.78345C14.9312 9.46484 14.4734 8.23804 14.8687 7.04858Z'
                  fill='#4C737F'
                />
                <path
                  d='M22.8691 10.6498L22.8686 10.6493C21.5281 9.65904 19.5232 10.0882 18.3992 11.6065C17.2762 13.1256 17.4518 15.1678 18.7903 16.1587C19.2784 16.5204 19.8552 16.6932 20.4455 16.6932C21.4744 16.6932 22.5449 16.1681 23.2602 15.2024C24.3832 13.6833 24.2078 11.6412 22.8691 10.6498ZM22.1296 14.3654C21.4658 15.2613 20.3436 15.5585 19.6274 15.0281C18.9119 14.4983 18.8683 13.3385 19.5302 12.4432C19.9702 11.8488 20.6129 11.5181 21.2053 11.5181C21.5048 11.5181 21.7916 11.6027 22.0325 11.7809C22.7475 12.3111 22.7909 13.4706 22.1296 14.3654Z'
                  fill='#4C737F'
                />
              </g>
              <defs>
                <clipPath id='clip0_23260_4482'>
                  <rect width='24' height='24' fill='white' transform='translate(0 0.598145)' />
                </clipPath>
              </defs>
            </svg>
          </div>
          <div className='text-sm md:text-base text-metal-gray'>
            {details?.requirement?.pet_allow?.toLowerCase() === 'true'
              ? 'Pets Allowed'
              : 'No Pets Allowed'}
          </div>
        </div>
      </div>
    </>
  );
};

export default PropertyHouseRules;
