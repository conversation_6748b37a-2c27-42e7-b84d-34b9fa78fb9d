import { Separator } from '@/app/ui/separator';
import { IListingDetails } from '@/types/rental-listing-details';

import Entertainment from './property-amenities/Entertainment';
import Essentials from './property-amenities/Essentials';
import KitchenAndDining from './property-amenities/KitchenAndDining';
import Laundry from './property-amenities/Laundry';
import Outdoors from './property-amenities/Outdoors';

type Props = {
  details: IListingDetails;
};

const AllAmenities = ({ details }: Props) => {
  return (
    <>
      <Separator className='my-6' />
      <div className='grid grid-cols-1 lg:grid-cols-2 gap-3'>
        <div>
          <div className='p-5 border border-solid border-platinium rounded-xl h-min mb-3'>
            <p className='font-medium text-lg my-3'>Essentials</p>
            <Separator />
            <Essentials details={details} />
          </div>
          <div className='p-5 border border-solid border-platinium rounded-xl h-min mb-3'>
            <p className='font-medium text-lg my-3'>Outdoors</p>
            <Separator />
            <Outdoors details={details} />
          </div>
        </div>
        <div>
          <div className='p-5 border border-solid border-platinium rounded-xl h-min mb-3'>
            <p className='font-medium text-lg my-3'>Entertainment</p>
            <Separator />
            <Entertainment details={details} />
          </div>
          <div className='p-5 border border-solid border-platinium rounded-xl h-min mb-3'>
            <p className='font-medium text-lg my-3'>Kitchen and Dining</p>
            <Separator />
            <KitchenAndDining details={details} />
          </div>
          <div className='p-5 border border-solid border-platinium rounded-xl h-min'>
            <p className='font-medium text-lg my-3'>Laundry</p>
            <Separator />
            <Laundry details={details} />
          </div>
        </div>
      </div>
    </>
  );
};

export default AllAmenities;
