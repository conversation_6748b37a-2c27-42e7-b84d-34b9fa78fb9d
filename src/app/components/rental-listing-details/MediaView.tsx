import MobileMediaView from '@/clients/components/rental-listing-details/MobileMediaView';
import { IListingImage } from '@/types/rental-listing-details';

import Image from 'next/image';

export const VIEW_ALL_PHOTOS_ID = `RentalDetails_View-All-Photos`;
export const IMAGE_COUNT_LABEL_ID = `RentalDetails_Image-Count-Label`;
export const VIRTUAL_TOUR_ID = `RentalDetails_Virtual-Tour`;

type Props = {
  images: IListingImage[];
  isMobile?: boolean;
  propertyHeadline?: string;
  className?: string;
  virtual_tour_link?: string;
};

const MediaView = ({
  images,
  isMobile,
  propertyHeadline = '',
  className = '',
  virtual_tour_link,
}: Props) => {
  const imageCountLabel = images.length ? `View ${images.length} images` : '';

  return (
    <div className={`-ml-5 -mr-5 w-[calc(100%+40px)] md:ml-0 md:mr-0 ${className}`}>
      <div className='relative'>
        {isMobile ? (
          <MobileMediaView
            images={images}
            propertyHeadline={propertyHeadline}
            virtual_tour_link={virtual_tour_link}
          >
            <Image
              alt={images[0]?.alt_text ?? ''}
              src={images[0]?.url ?? 'https://placehold.co/150'}
              fill
              sizes='320px'
              className='object-cover'
              priority
              placeholder='blur'
              blurDataURL='https://placehold.co/150'
            />
            <span className='block sm:hidden absolute bottom-2.5 left-2.5 px-2.5 py-1.5 bg-[#8BABB6] text-sm text-white rounded rounded-2xl cursor-pointer'>
              {imageCountLabel}
            </span>
            {!!virtual_tour_link && (
              <span
                id={VIRTUAL_TOUR_ID}
                className='block sm:hidden absolute bottom-2.5 left-[150px] px-2.5 py-1.5 bg-[#8BABB6] text-sm text-white rounded rounded-2xl cursor-pointer'
              >
                3D Tour
              </span>
            )}
          </MobileMediaView>
        ) : (
          <Image
            alt={images[0]?.alt_text ?? ''}
            src={images[0]?.url ?? 'https://placehold.co/150'}
            width={0}
            height={0}
            sizes='60vw'
            className='rounded-0 h-[450px] w-full object-cover cursor-pointer md:rounded'
            priority
            placeholder='blur'
            blurDataURL='https://placehold.co/150'
          />
        )}

        <span
          id={IMAGE_COUNT_LABEL_ID}
          className='hidden sm:block absolute bottom-2.5 left-2.5 px-2.5 py-1.5 bg-[#8BABB6] text-sm text-white rounded rounded-2xl cursor-pointer'
        >
          {imageCountLabel}
        </span>
      </div>
      <div className='mt-2.5 hidden h-full w-full flex-row  justify-between gap-2 md:flex'>
        <Image
          alt={images[0]?.alt_text ?? ''}
          src={images[0]?.url ?? 'https://placehold.co/150'}
          width={195}
          height={132}
          sizes='10vw'
          className='h-[100px] w-[20%] rounded object-cover cursor-pointer'
          priority
        />
        <Image
          alt={images[1]?.alt_text ?? ''}
          src={images[1]?.url ?? 'https://placehold.co/150'}
          width={195}
          height={132}
          sizes='10vw'
          className='h-[100px] w-[20%] rounded object-cover cursor-pointer'
          priority
        />
        <Image
          alt={images[2]?.alt_text ?? ''}
          src={images[2]?.url ?? 'https://placehold.co/150'}
          width={195}
          height={132}
          sizes='10vw'
          className='h-[100px] w-[20%] rounded object-cover cursor-pointer'
          priority
        />
        {!!virtual_tour_link ? (
          <div className='relative w-[20%] overflow-hidden rounded'>
            <Image
              alt={images[3]?.alt_text ?? ''}
              src={images[3]?.url ?? 'https://placehold.co/150'}
              width={195}
              height={132}
              sizes='10vw'
              className='h-[100px] w-full rounded object-cover cursor-pointer'
              priority
            />
            <div
              id={VIRTUAL_TOUR_ID}
              className='absolute inset-0 flex cursor-pointer items-center justify-center bg-[rgba(139,171,182,0.70)] text-sm text-white'
            >
              3D Tour
            </div>
          </div>
        ) : (
          <Image
            alt={images[3]?.alt_text ?? ''}
            src={images[3]?.url ?? 'https://placehold.co/150'}
            width={195}
            height={132}
            sizes='10vw'
            className='h-[100px] w-[20%] rounded object-cover cursor-pointer'
            priority
          />
        )}

        <div className='relative w-[20%] overflow-hidden rounded'>
          {images[4] ? (
            <Image
              alt={images[4]?.alt_text ?? ''}
              src={images[4]?.url ?? 'https://placehold.co/150'}
              width={195}
              height={132}
              sizes='10vw'
              className='h-[100px] w-full rounded object-cover cursor-pointer'
              priority
            />
          ) : null}
          <div
            id={VIEW_ALL_PHOTOS_ID}
            className='absolute inset-0 flex cursor-pointer items-center justify-center bg-[rgba(139,171,182,0.70)] text-sm text-white'
          >
            View All Photos
          </div>
        </div>
      </div>
    </div>
  );
};

export default MediaView;
