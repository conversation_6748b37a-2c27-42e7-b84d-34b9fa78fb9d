import React from 'react';

import AmenityItem from '@/app/components/rental-listing-details/AmenityItem';
import { IListingDetails } from '@/types/rental-listing-details';
import {
  getAmenityIconSrcPath,
  getAmenityQuantity,
  getAmenityTitle,
  getAmenityType,
} from '@/utils/amenity';

type Props = {
  details: IListingDetails;
};

const KitchenAndDining = ({ details }: Props) => {
  return (
    <>
      <AmenityItem
        title='Seated Dining For'
        iconPath={getAmenityIconSrcPath('seating')}
        quantity={details.seating ?? 0}
        className='mb-5'
        isAvailable
      />
      <AmenityItem
        title='Dining Areas'
        iconPath={getAmenityIconSrcPath('dining_area')}
        className='mb-5'
        isAvailable={details.dining_area}
      />
      <AmenityItem
        title='Dining Room'
        iconPath={getAmenityIconSrcPath('dining_room')}
        className='mb-5'
        isAvailable={details.dining_room}
      />
      <AmenityItem
        title='Grill'
        iconPath={getAmenityIconSrcPath('grill')}
        type={getAmenityType('grill', details)}
        className='mb-5'
        isAvailable={details.grill}
      />
      <AmenityItem
        title='Microwave'
        iconPath={getAmenityIconSrcPath('microwave')}
        type={getAmenityType('microwave', details)}
        quantity={getAmenityQuantity('microwave', details)}
        className='mb-5'
        isAvailable={details.microwave}
      />
      <AmenityItem
        title='Refrigerator'
        iconPath={getAmenityIconSrcPath('refrigerator')}
        type={getAmenityType('refrigerator', details)}
        quantity={getAmenityQuantity('refrigerator', details)}
        className='mb-5'
        isAvailable={details?.refrigerator}
      />
      <AmenityItem
        title={getAmenityTitle('blender')}
        iconPath={getAmenityIconSrcPath('blender')}
        type={getAmenityType('blender', details)}
        quantity={getAmenityQuantity('blender', details)}
        className='mb-5'
        isAvailable={details?.blender}
      />
      <AmenityItem
        title={getAmenityTitle('bbq_tool')}
        iconPath={getAmenityIconSrcPath('bbq_tool')}
        type={getAmenityType('bbq_tool', details)}
        quantity={getAmenityQuantity('bbq_tool', details)}
        className='mb-5'
        isAvailable={details?.bbq_tool}
      />
      <AmenityItem
        title='Coffee Maker'
        iconPath={getAmenityIconSrcPath('coffee_maker')}
        type={getAmenityType('coffee_maker', details)}
        quantity={getAmenityQuantity('coffee_maker', details)}
        className='mb-5'
        isAvailable={details?.coffee_maker}
      />
      <AmenityItem
        title='Dishes, Cups and Utensils'
        iconPath={getAmenityIconSrcPath('dish_or_cup_or_utensil')}
        type={getAmenityType('dish_or_cup_or_utensil', details)}
        quantity={getAmenityQuantity('dish_or_cup_or_utensil', details)}
        className='mb-5'
        isAvailable={details?.dish_or_cup_or_utensil}
      />
      <AmenityItem
        title='Dishwasher'
        iconPath={getAmenityIconSrcPath('dishwasher')}
        type={getAmenityType('dishwasher', details)}
        quantity={getAmenityQuantity('dishwasher', details)}
        className='mb-5'
        isAvailable={details?.dishwasher}
      />
      <AmenityItem
        title='Food Processor'
        iconPath={getAmenityIconSrcPath('food_processor')}
        type={getAmenityType('food_processor', details)}
        quantity={getAmenityQuantity('food_processor', details)}
        className='mb-5'
        isAvailable={details?.food_processor}
      />
      <AmenityItem
        title={getAmenityTitle('ice_maker')}
        iconPath={getAmenityIconSrcPath('ice_maker')}
        type={getAmenityType('ice_maker', details)}
        quantity={getAmenityQuantity('ice_maker', details)}
        className='mb-5'
        isAvailable={details?.ice_maker}
      />
      <AmenityItem
        title={getAmenityTitle('ice_tray')}
        iconPath={getAmenityIconSrcPath('ice_tray')}
        type={getAmenityType('ice_tray', details)}
        quantity={getAmenityQuantity('ice_tray', details)}
        className='mb-5'
        isAvailable={details?.ice_tray}
      />
      <AmenityItem
        title={getAmenityTitle('oven')}
        iconPath={getAmenityIconSrcPath('oven')}
        type={getAmenityType('oven', details)}
        quantity={getAmenityQuantity('oven', details)}
        className='mb-5'
        isAvailable={details?.oven}
      />
      <AmenityItem
        title={getAmenityTitle('stove')}
        iconPath={getAmenityIconSrcPath('stove')}
        type={getAmenityType('stove', details)}
        quantity={getAmenityQuantity('stove', details)}
        className='mb-5'
        isAvailable={details?.stove}
      />
      <AmenityItem
        title='Pots and Pans'
        iconPath={getAmenityIconSrcPath('pot_or_pan')}
        type={getAmenityType('pot_or_pan', details)}
        quantity={getAmenityQuantity('pot_or_pan', details)}
        className='mb-5'
        isAvailable={details?.pot_or_pan}
      />
      <AmenityItem
        title={getAmenityTitle('tea_kettle')}
        iconPath={getAmenityIconSrcPath('tea_kettle')}
        type={getAmenityType('tea_kettle', details)}
        quantity={getAmenityQuantity('tea_kettle', details)}
        className='mb-5'
        isAvailable={details?.tea_kettle}
      />
      <AmenityItem
        title={getAmenityTitle('toaster')}
        iconPath={getAmenityIconSrcPath('toaster')}
        type={getAmenityType('toaster', details)}
        quantity={getAmenityQuantity('toaster', details)}
        className='mb-5'
        isAvailable={details?.toaster}
      />
    </>
  );
};

export default KitchenAndDining;
