import React from 'react';

import AmenityItem from '@/app/components/rental-listing-details/AmenityItem';
import { IListingDetails } from '@/types/rental-listing-details';
import {
  getAmenityIconSrcPath,
  getAmenityQuantity,
  getAmenityTitle,
  getAmenityType,
} from '@/utils/amenity';

type Props = {
  details: IListingDetails;
};

const OUTDOOR_AMENITY_KEYS = [
  'pool',
  'parking',
  'tennis_court',
  'porch',
  'patio',
  'yard',
  'deck',
  'outdoor_shower',
  'outdoor_furniture',
  'outdoor_dining_area',
  'beach_towel',
  'beach_chair',
  'beach_umbrella',
  'bicycle',
  'cooler',
  'lawn_or_garden',
];

const Outdoors = ({ details }: Props) => {
  return (
    <>
      {OUTDOOR_AMENITY_KEYS.map((_key, index) => (
        <AmenityItem
          key={index}
          title={getAmenityTitle(_key)}
          iconPath={getAmenityIconSrcPath(_key)}
          quantity={getAmenityQuantity(_key, details)}
          type={getAmenityType(_key, details)}
          className='mb-5'
          isAvailable={details?.[_key as unknown as keyof IListingDetails]}
        />
      ))}
    </>
  );
};

export default Outdoors;
