import React from 'react';

import AmenityItem from '@/app/components/rental-listing-details/AmenityItem';
import { IListingDetails } from '@/types/rental-listing-details';
import {
  getAmenityIconSrcPath,
  getAmenityQuantity,
  getAmenityTitle,
  getAmenityType,
} from '@/utils/amenity';

type Props = {
  details: IListingDetails;
};

const ENTERTAINMENT_AMENITY_KEYS = ['tv', 'dvd_player', 'bluetooth_speaker', 'book', 'sonos'];

const Entertainment = ({ details }: Props) => {
  return (
    <>
      {ENTERTAINMENT_AMENITY_KEYS.map((_key, index) => (
        <AmenityItem
          key={index}
          title={getAmenityTitle(_key)}
          iconPath={getAmenityIconSrcPath(_key)}
          quantity={getAmenityQuantity(_key, details)}
          type={getAmenityType(_key, details)}
          className='mb-5'
          isAvailable={details?.[_key as unknown as keyof IListingDetails]}
        />
      ))}
    </>
  );
};

export default Entertainment;
