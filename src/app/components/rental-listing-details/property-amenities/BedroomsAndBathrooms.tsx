import React from 'react';

import AmenityItem from '@/app/components/rental-listing-details/AmenityItem';
import { IListingDetails } from '@/types/rental-listing-details';
import { getAmenityIconSrcPath } from '@/utils/amenity';

type Props = {
  details: IListingDetails;
};

const BedroomsAndBathrooms = ({ details }: Props) => {
  return (
    <>
      <AmenityItem
        title='Sleeping Capacity'
        iconPath={getAmenityIconSrcPath('sleeping_capacity')}
        quantity={details.capacity ?? 0}
        className='mb-5'
        isAvailable
      />
      <AmenityItem
        title='Total Bedrooms'
        iconPath={getAmenityIconSrcPath('dining_area')}
        quantity={details.bedroom_number ?? 0}
        className='mb-5'
        isAvailable
      />
      <AmenityItem
        title='Total Bathrooms'
        iconPath={getAmenityIconSrcPath('dining_area')}
        type={`${details.bathroom_number} full${
          (details?.half_bathroom_number ?? 0) > 0 ? `, ${details.half_bathroom_number} half` : ``
        }`}
        className='mb-5'
        isAvailable
      />
    </>
  );
};

export default BedroomsAndBathrooms;
