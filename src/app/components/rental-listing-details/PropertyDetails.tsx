import { UserIcon } from '@heroicons/react/24/outline';

import SvgBathroom from '@/app/assets/svgs/Bathroom';
import Bedroom from '@/app/assets/svgs/Bedroom';
import { Separator } from '@/app/ui/separator';
import ShareMenu from '@/clients/components/share-menu';
import { IListingDetails } from '@/types/rental-listing-details';
import { getStringSingularPlural } from '@/utils/common';

type Props = {
  details: IListingDetails;
};
const prefix = process.env.VERCEL_ENV === 'production' ? 'https://www' : 'https://dev';

const PropertyDetails = ({ details }: Props) => {
  return (
    <div className='border border-none md:border-solid border-platinium md:px-2 py-3 lg:p-6 w-full flex items-center justify-between my-4 md:my-[30px] rounded-2xl text-metal-gray'>
      <div className='flex items-center md:justify-center px-4 md:px-0 gap-x-2 text-sm md:text-base'>
        <Bedroom className='w-5 h-5' color='#4C737F' />
        <span className='hidden md:block'>
          {getStringSingularPlural('Bedroom', 'Bedrooms', details.bedroom_number)}
        </span>
        <span className='md:hidden'>{details.bedroom_number} BR</span>
      </div>
      <Separator orientation='vertical' className='h-6 hidden md:block' />
      <div className='flex items-center md:justify-center px-4 md:px-0 gap-x-2 text-sm md:text-base'>
        <SvgBathroom className='w-5 h-5' color='#4C737F' />
        <span className='hidden md:block'>
          {`${details.bathroom_number} full ${
            (details?.half_bathroom_number ?? 0) > 0 ? `, ${details.half_bathroom_number} half` : ''
          }`}
        </span>
        <span className='md:hidden'>
          {getStringSingularPlural('Bath', 'Baths', details.bathroom_number)}
        </span>
      </div>
      <Separator orientation='vertical' className='h-6 hidden md:block' />
      <div className='flex items-center md:justify-center px-4 md:px-0 gap-x-2 text-sm md:text-base'>
        <UserIcon className='w-5 h-5 text-[#4C737F]' />
        {getStringSingularPlural('Guest', 'Guests', details.capacity)}
      </div>
    </div>
  );
};

export default PropertyDetails;
