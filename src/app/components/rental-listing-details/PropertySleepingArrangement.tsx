import React from 'react';

import Bedroom from '@/app/assets/svgs/Bedroom';
import { Separator } from '@/app/ui/separator';
import AllSleepingAreasTrigger from '@/clients/components/rental-listing-details/AllSleepingAreasTrigger';
import { IListingBedroom } from '@/types/rental-listing-details';

type Props = {
  bedrooms: IListingBedroom[];
};

const PropertySleepingArrangement = ({ bedrooms }: Props) => {
  return (
    <>
      <AllSleepingAreasTrigger
        showButton={bedrooms.length > 4}
        allSleepingAreasNode={bedrooms.map((_b, index) => (
          <div
            className='flex items-center justify-between px-4 md:px-6 py-4 rounded-2xl border border-solid border-platinium text-metal-gray gap-x-2 md:gap-x-0'
            key={index}
          >
            <div className='flex text-sm md:text-base items-center gap-x-2 md:gap-x-4 w-4/12'>
              <Bedroom className='w-5 h-5' color='#4C737F' />
              Bedroom {index + 1}
            </div>
            <p className='m-0 w-4/12 text-sm md:text-base'>
              {_b.beds.map((_bed) => (
                <span key={_bed.bed_uuid} className='mr-2'>
                  {_bed.type.name} {_bed.number > 1 && `(${_bed.number})`}
                </span>
              ))}
            </p>
            <p className='m-0 md:text-right w-4/12 text-sm md:text-base'>
              {_b.floor_level?.name ?? ''}
            </p>
          </div>
        ))}
      >
        {bedrooms.slice(0, 4).map((_b, index) => (
          <div
            className='flex items-center justify-between px-4 md:px-6 py-4 rounded-2xl border border-solid border-platinium text-metal-gray gap-x-2 md:gap-x-0'
            key={index}
          >
            <div className='flex text-sm md:text-base items-center gap-x-2 md:gap-x-4 w-4/12'>
              <Bedroom className='w-5 h-5' color='#4C737F' />
              Bedroom {index + 1}
            </div>
            <p className='m-0 w-4/12 text-sm md:text-base'>
              {_b.beds.map((_bed) => (
                <span key={_bed.bed_uuid} className='mr-2'>
                  {_bed.type.name} {_bed.number > 1 && `(${_bed.number})`}
                </span>
              ))}
            </p>
            <p className='m-0 md:text-right w-4/12 text-sm md:text-base'>
              {_b.floor_level?.name ?? ''}
            </p>
          </div>
        ))}
      </AllSleepingAreasTrigger>
      <Separator className='my-2 md:my-3 bg-transparent' />
    </>
  );
};

export default PropertySleepingArrangement;
