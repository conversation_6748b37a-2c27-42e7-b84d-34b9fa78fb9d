import AllAmenitiesTrigger from '@/clients/components/rental-listing-details/AllAmenitiesTrigger';
import { IListingDetails } from '@/types/rental-listing-details';
import { getAmenityIconSrcPath, getAmenityQuantity, getAmenityTitle } from '@/utils/amenity';

import AllAmenities from './AllAmenities';
import AmenityItem from './AmenityItem';

type Props = {
  details: IListingDetails;
};

const PropertyFeaturedAmenities = ({ details }: Props) => {
  return (
    <AllAmenitiesTrigger allAmenitiesNode={<AllAmenities details={details} />}>
      <div className='grid grid-cols-2 gap-2 md:gap-3'>
        {(details.featured_amenities ?? []).map((_fa, index) => (
          <div
            key={index}
            className='border border-solid border-platinium px-4 md:px-5 w-full h-[50px] flex items-center justify-start rounded-lg gap-x-3 text-metal-gray'
          >
            <AmenityItem
              title={getAmenityTitle(_fa)}
              iconPath={getAmenityIconSrcPath(_fa)}
              quantity={getAmenityQuantity(_fa, details)}
              hideCheck
            />
          </div>
        ))}
      </div>
    </AllAmenitiesTrigger>
  );
};

export default PropertyFeaturedAmenities;
