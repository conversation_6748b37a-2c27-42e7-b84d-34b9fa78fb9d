import { getLeadDetails } from '@/app/services/lead-request';
import LeadActionButtons from '@/clients/components/lead-request/LeadActionButtons';
import { Lead } from '@/types/lead';

import dayjs from 'dayjs';

import LeadAcceptedDetails from '../../components/lead-request/LeadAcceptedDetails';
import LeadRequestDetails from '../../components/lead-request/LeadRequestDetails';
import LeadRequestHeader from '../../components/lead-request/LeadRequestHeader';

type PageProps = {
  params: Promise<{ slug: string | string[] }>;
};

export default async function LeadRequest({ params }: PageProps) {
  const { slug } = await params;

  // Ensure slug is an array and has at least 2 elements
  if (!Array.isArray(slug) || slug.length < 2) {
    throw new Error('Invalid slug parameters');
  }

  const [distributionUuid, user] = slug;
  const leadDetails = await getLeadDetails<Lead>(distributionUuid, user);
  console.log(leadDetails);

  const expired = dayjs().isAfter(leadDetails?.expires_at) || !leadDetails?.expires_at;
  const accepted = leadDetails?.status === 'accepted';
  const declined = leadDetails?.status === 'declined';

  return (
    <div className='-mt-[80px] md:-mt-[90px]'>
      <LeadRequestHeader
        expired={expired}
        accepted={accepted}
        declined={declined}
        leadDetails={leadDetails}
      />
      <div className='px-5 py-5 md:px-0 w-full md:w-[400px] mx-auto'>
        {expired ? (
          <div>
            <p className='text-xl font-bold'>Time Expired</p>
            <p className='text-sm'>This lead is no longer available for assignment.</p>
          </div>
        ) : accepted ? (
          <LeadAcceptedDetails leadDetails={leadDetails} />
        ) : declined ? (
          <></>
        ) : (
          <>
            <LeadRequestDetails leadDetails={leadDetails} />
            <LeadActionButtons
              distribution_uuid={leadDetails.distribution_uuid}
              user={leadDetails.user}
            />
          </>
        )}
      </div>
    </div>
  );
}
