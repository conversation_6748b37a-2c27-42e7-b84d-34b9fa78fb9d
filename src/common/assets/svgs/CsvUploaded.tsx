import * as React from 'react';
import { SVGProps } from 'react';

const SvgCsvUploaded = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={80}
    height={80}
    viewBox='0 0 80 80'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
    {...props}
  >
    <path
      d='M72.576 3.428H7.43a4 4 0 0 0-4 4v54.585a4 4 0 0 0 4 4h65.147a4 4 0 0 0 4-4V7.428a4 4 0 0 0-4-4Z'
      fill='#fff'
      stroke='#6D7380'
      strokeMiterlimit={10}
    />
    <path
      d='M52.483 21.714H16.09c-.37-.038-.704-.178-.932-.392-.228-.213-.332-.483-.29-.75-.043-.269.062-.538.29-.752.228-.213.562-.354.931-.392h36.395c.37.038.704.18.932.392.228.214.333.483.29.751.043.268-.062.538-.29.751-.228.214-.562.354-.931.392ZM61.28 16H16.435c-.418 0-.819-.12-1.115-.335-.295-.214-.462-.505-.462-.808 0-.303.167-.594.462-.808.296-.214.697-.335 1.115-.335h44.847c.418 0 .819.12 1.115.335.295.214.461.505.461.808 0 .303-.166.594-.461.808-.296.214-.697.335-1.115.335ZM48.017 33.143H15.984c-.299 0-.585-.12-.797-.335a1.151 1.151 0 0 1 0-1.616c.212-.215.498-.335.797-.335h32.033c.298 0 .585.12.796.335a1.151 1.151 0 0 1 0 1.616 1.118 1.118 0 0 1-.796.335ZM40.74 27.428H16.403c-.41 0-.803-.12-1.094-.334-.29-.215-.453-.505-.453-.808 0-.303.163-.594.453-.808.29-.215.684-.335 1.094-.335H40.74c.41 0 .804.12 1.094.335.29.214.453.505.453.808 0 .303-.163.593-.453.808-.29.214-.683.334-1.094.334Z'
      fill='#8DE1FF'
    />
    <path
      d='M39.666 76.571c8.08 0 14.63-6.593 14.63-14.725 0-8.133-6.55-14.726-14.63-14.726-8.08 0-14.63 6.593-14.63 14.726 0 8.132 6.55 14.725 14.63 14.725Z'
      fill='#14A800'
      stroke='#6D7380'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
    <path
      d='m32.878 61.573 6.334 5.817 8.478-10.374'
      stroke='#fff'
      strokeWidth={4}
      strokeLinecap='round'
      strokeLinejoin='round'
    />
  </svg>
);

export default SvgCsvUploaded;
