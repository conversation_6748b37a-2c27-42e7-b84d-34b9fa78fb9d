'use client';

import * as React from 'react';

import * as TooltipPrimitive from '@radix-ui/react-tooltip';

import { cn } from '@/lib/utils';

const TooltipProvider = ({
  delayDuration = 300,
  skipDelayDuration = 300,
  disableHoverableContent = false,
  children,
}: TooltipPrimitive.TooltipProviderProps) => (
  <TooltipPrimitive.Provider
    delayDuration={delayDuration}
    skipDelayDuration={skipDelayDuration}
    disableHoverableContent={disableHoverableContent}
  >
    {children}
  </TooltipPrimitive.Provider>
);

const Tooltip = TooltipPrimitive.Root;

// Enhanced trigger that handles clicks on mobile
const TooltipTrigger = React.forwardRef<
  React.ElementRef<typeof TooltipPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Trigger> & {
    disableMobileSupport?: boolean;
  }
>(({ disableMobileSupport = false, ...props }, ref) => {
  const [isMounted, setIsMounted] = React.useState(false);
  const [isMobile, setIsMobile] = React.useState(false);
  const [isOpen, setIsOpen] = React.useState(false);
  const contextValue = React.useContext(TooltipContext);

  React.useEffect(() => {
    setIsMounted(true);
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  // Don't render special handling during SSR
  if (!isMounted) return <TooltipPrimitive.Trigger ref={ref} {...props} />;

  // For mobile devices, handle click to toggle tooltip
  if (isMobile && !disableMobileSupport) {
    return (
      <TooltipPrimitive.Trigger
        ref={ref}
        {...props}
        onClick={(e) => {
          // Call the original onClick if it exists
          if (props.onClick) {
            props.onClick(e);
          }

          // Toggle the tooltip
          setIsOpen(!isOpen);

          // Update the context value
          if (contextValue) {
            contextValue.setOpen(!isOpen);
          }

          // Prevent event propagation
          e.stopPropagation();
        }}
      />
    );
  }

  // For desktop, use the default behavior
  return <TooltipPrimitive.Trigger ref={ref} {...props} />;
});
TooltipTrigger.displayName = TooltipPrimitive.Trigger.displayName;

// Create a context to manage tooltip state
const TooltipContext = React.createContext<{
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
} | null>(null);

// Enhanced tooltip that provides context for mobile support
const TooltipRoot = React.forwardRef<
  React.ElementRef<typeof TooltipPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Root>
>(({ open, defaultOpen, onOpenChange, ...props }) => {
  const [isOpen, setIsOpen] = React.useState(defaultOpen || false);

  // Handle controlled and uncontrolled modes
  const handleOpenChange = React.useCallback(
    (newOpen: boolean) => {
      setIsOpen(newOpen);
      onOpenChange?.(newOpen);
    },
    [onOpenChange],
  );

  const contextValue = React.useMemo(
    () => ({
      open: open !== undefined ? open : isOpen,
      setOpen: (newOpen: boolean) => {
        if (open === undefined) {
          setIsOpen(newOpen);
        }
        handleOpenChange(newOpen);
      },
    }),
    [open, isOpen, handleOpenChange],
  );

  return (
    <TooltipContext.Provider value={contextValue as any}>
      <TooltipPrimitive.Root
        open={open !== undefined ? open : isOpen}
        defaultOpen={defaultOpen}
        onOpenChange={handleOpenChange}
        {...props}
      />
    </TooltipContext.Provider>
  );
});
TooltipRoot.displayName = TooltipPrimitive.Root.displayName;

const TooltipContent = React.forwardRef<
  React.ElementRef<typeof TooltipPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content> & {
    mobileClassName?: string;
  }
>(({ className, mobileClassName, sideOffset = 4, ...props }, ref) => {
  const [isMounted, setIsMounted] = React.useState(false);
  const [isMobile, setIsMobile] = React.useState(false);

  React.useEffect(() => {
    setIsMounted(true);
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  // Don't render during SSR to prevent hydration mismatch
  if (!isMounted) return null;

  // For mobile devices, use a different style if provided
  const tooltipClassName =
    isMobile && mobileClassName
      ? mobileClassName
      : cn(
          'z-50 overflow-hidden rounded-md bg-primary px-3 py-1.5 text-xs text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-tooltip-content-transform-origin]',
          className,
        );

  return (
    <TooltipPrimitive.Portal>
      <TooltipPrimitive.Content
        ref={ref}
        sideOffset={sideOffset}
        className={tooltipClassName}
        {...props}
      />
    </TooltipPrimitive.Portal>
  );
});
TooltipContent.displayName = TooltipPrimitive.Content.displayName;

export { TooltipRoot as Tooltip, TooltipTrigger, TooltipContent, TooltipProvider };
