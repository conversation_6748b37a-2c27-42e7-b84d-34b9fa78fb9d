'use client';

import { Fragment } from 'react';

import { Dialog, Transition } from '@headlessui/react';
import { XMarkIcon } from '@heroicons/react/20/solid';

import { twMerge } from 'tailwind-merge';

type Props = {
  children?: React.ReactNode;
  open: boolean;
  onClose?: () => void;
  className?: string;
  showClose?: boolean;
};
const Modal = ({ children, open, onClose, className, showClose }: Props) => (
  <Transition appear show={open} as={Fragment}>
    <Dialog as='div' className='relative z-50' onClose={() => onClose?.()}>
      <Transition.Child
        as={Fragment}
        enter='ease-out duration-300'
        enterFrom='opacity-0'
        enterTo='opacity-100'
        leave='ease-in duration-200'
        leaveFrom='opacity-100'
        leaveTo='opacity-0'
      >
        <div className='fixed inset-0 bg-white bg-opacity-100 md:bg-backdrop md:bg-opacity-80' />
      </Transition.Child>

      <div className='fixed inset-0 overflow-y-auto'>
        <div className='relative flex min-h-[100vh] md:min-h-full items-center justify-center text-center md:p-4'>
          {showClose && (
            <div
              className='absolute right-2.5 top-2.5 z-[1] h-8 w-8 cursor-pointer bg-transparent md:bg-[rgba(9,24,44)] md:text-white'
              onClick={onClose}
            >
              <XMarkIcon className='fill-current' />
            </div>
          )}
          <Transition.Child
            as={Fragment}
            enter='ease-out duration-300'
            enterFrom='opacity-0 scale-95'
            enterTo='opacity-100 scale-100'
            leave='ease-in duration-200'
            leaveFrom='opacity-100 scale-100'
            leaveTo='opacity-0 scale-95'
          >
            <Dialog.Panel
              className={twMerge(
                'min-w-[100vw] md:min-w-[75vw] lg:min-w-[600px] transform bg-white text-left align-middle shadow-xl transition-all md:rounded-2xl',
                className,
              )}
            >
              {children}
            </Dialog.Panel>
          </Transition.Child>
        </div>
      </div>
    </Dialog>
  </Transition>
);

export default Modal;
