import * as React from 'react';

import { CheckIcon, ChevronUpIcon } from '@heroicons/react/24/outline';
import * as RadixSelectPrimitive from '@radix-ui/react-select';

import DownTriangle from '@/app/assets/svgs/DownTriangle';
import { cn } from '@/lib/utils';

const RadixSelect = RadixSelectPrimitive.Root;

const RadixSelectGroup = RadixSelectPrimitive.Group;

const RadixSelectValue = RadixSelectPrimitive.Value;

const RadixSelectTrigger = React.forwardRef<
  React.ElementRef<typeof RadixSelectPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof RadixSelectPrimitive.Trigger>
>(({ className, children, ...props }, ref) => (
  <RadixSelectPrimitive.Trigger
    ref={ref}
    className={cn(
      'flex font-poppins h-9 w-full items-center justify-between whitespace-nowrap text-black rounded-md border border-solid border-platinium bg-transparent px-3 py-2 text-sm shadow-sm placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1 focus-visible:outline-none',
      className,
    )}
    {...props}
  >
    {children}
    <RadixSelectPrimitive.Icon asChild>
      <DownTriangle className='w-6 h-6 text-[#6D7381]' />
    </RadixSelectPrimitive.Icon>
  </RadixSelectPrimitive.Trigger>
));
RadixSelectTrigger.displayName = RadixSelectPrimitive.Trigger.displayName;

const RadixSelectScrollUpButton = React.forwardRef<
  React.ElementRef<typeof RadixSelectPrimitive.ScrollUpButton>,
  React.ComponentPropsWithoutRef<typeof RadixSelectPrimitive.ScrollUpButton>
>(({ className, ...props }, ref) => (
  <RadixSelectPrimitive.ScrollUpButton
    ref={ref}
    className={cn('flex cursor-default items-center justify-center py-1', className)}
    {...props}
  >
    <ChevronUpIcon className='h-4 w-4' />
  </RadixSelectPrimitive.ScrollUpButton>
));
RadixSelectScrollUpButton.displayName = RadixSelectPrimitive.ScrollUpButton.displayName;

const RadixSelectScrollDownButton = React.forwardRef<
  React.ElementRef<typeof RadixSelectPrimitive.ScrollDownButton>,
  React.ComponentPropsWithoutRef<typeof RadixSelectPrimitive.ScrollDownButton>
>(({ className, ...props }, ref) => (
  <RadixSelectPrimitive.ScrollDownButton
    ref={ref}
    className={cn('flex cursor-default items-center justify-center py-1', className)}
    {...props}
  >
    <DownTriangle className='h-4 w-4' />
  </RadixSelectPrimitive.ScrollDownButton>
));
RadixSelectScrollDownButton.displayName = RadixSelectPrimitive.ScrollDownButton.displayName;

const RadixSelectContent = React.forwardRef<
  React.ElementRef<typeof RadixSelectPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof RadixSelectPrimitive.Content>
>(({ className, children, position = 'popper', ...props }, ref) => (
  <RadixSelectPrimitive.Portal>
    <RadixSelectPrimitive.Content
      ref={ref}
      className={cn(
        'relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',
        position === 'popper' &&
          'data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1',
        className,
      )}
      position={position}
      {...props}
    >
      <RadixSelectScrollUpButton />
      <RadixSelectPrimitive.Viewport
        className={cn(
          'p-1',
          position === 'popper' &&
            'h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]',
        )}
      >
        {children}
      </RadixSelectPrimitive.Viewport>
      <RadixSelectScrollDownButton />
    </RadixSelectPrimitive.Content>
  </RadixSelectPrimitive.Portal>
));
RadixSelectContent.displayName = RadixSelectPrimitive.Content.displayName;

const RadixSelectLabel = React.forwardRef<
  React.ElementRef<typeof RadixSelectPrimitive.Label>,
  React.ComponentPropsWithoutRef<typeof RadixSelectPrimitive.Label>
>(({ className, ...props }, ref) => (
  <RadixSelectPrimitive.Label
    ref={ref}
    className={cn('px-2 py-1.5 text-sm font-semibold', className)}
    {...props}
  />
));
RadixSelectLabel.displayName = RadixSelectPrimitive.Label.displayName;

const RadixSelectItem = React.forwardRef<
  React.ElementRef<typeof RadixSelectPrimitive.Item>,
  React.ComponentPropsWithoutRef<typeof RadixSelectPrimitive.Item>
>(({ className, children, ...props }, ref) => (
  <RadixSelectPrimitive.Item
    ref={ref}
    className={cn(
      'relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',
      className,
    )}
    {...props}
  >
    <span className='absolute right-2 flex h-3.5 w-3.5 items-center justify-center'>
      <RadixSelectPrimitive.ItemIndicator>
        <CheckIcon className='h-4 w-4' />
      </RadixSelectPrimitive.ItemIndicator>
    </span>
    <RadixSelectPrimitive.ItemText>{children}</RadixSelectPrimitive.ItemText>
  </RadixSelectPrimitive.Item>
));
RadixSelectItem.displayName = RadixSelectPrimitive.Item.displayName;

const RadixSelectSeparator = React.forwardRef<
  React.ElementRef<typeof RadixSelectPrimitive.Separator>,
  React.ComponentPropsWithoutRef<typeof RadixSelectPrimitive.Separator>
>(({ className, ...props }, ref) => (
  <RadixSelectPrimitive.Separator
    ref={ref}
    className={cn('-mx-1 my-1 h-px bg-muted', className)}
    {...props}
  />
));
RadixSelectSeparator.displayName = RadixSelectPrimitive.Separator.displayName;

export {
  RadixSelect,
  RadixSelectGroup,
  RadixSelectValue,
  RadixSelectTrigger,
  RadixSelectContent,
  RadixSelectLabel,
  RadixSelectItem,
  RadixSelectSeparator,
  RadixSelectScrollUpButton,
  RadixSelectScrollDownButton,
};
