'use client';

import React, { MutableRefObject, useEffect, useRef, useState } from 'react';

import { SegmentEvents } from '@/types/analytics';
import { IListingDetails } from '@/types/rental-listing-details';

type Context = {
  checkoutRef: MutableRefObject<HTMLDivElement | null>;
  propertyInfoRef: MutableRefObject<HTMLDivElement | null>;
  locationRef: MutableRefObject<any>;
  availabilityRef: MutableRefObject<HTMLDivElement | null>;
  openContactAgentForm: boolean;
  setOpenContactAgentForm: (b: boolean) => void;
  openBookingForm: boolean;
  setOpenBookingForm: (b: boolean) => void;
};

export const RentalDetailsContext = React.createContext<Context>({} as Context);

const RentalDetailsContextContainer = ({
  children,
  listingDetails,
}: {
  children: React.ReactNode;
  listingDetails: IListingDetails;
}) => {
  const checkoutRef = useRef<null | HTMLDivElement>(null);
  const propertyInfoRef = useRef<null | HTMLDivElement>(null);
  const locationRef = useRef<null | HTMLDivElement>(null);
  const availabilityRef = useRef<null | HTMLDivElement>(null);
  const [openBookingForm, setOpenBookingForm] = useState<boolean>(false);
  const [openContactAgentForm, setOpenContactAgentForm] = useState<boolean>(false);

  // useEffect(() => {
  //   window?.analytics?.track(SegmentEvents.LISTING_DETAIL_VIEWED, {
  //     listing_id: listingDetails.listing_id,
  //     listing_name: listingDetails.address,
  //     listing_number_of_bedrooms: listingDetails.bedroom_number,
  //     listing_type: 'Rental Property',
  //     price: listingDetails.peak_rate,
  //     bathrooms: listingDetails.bathroom_number,
  //     bedrooms: listingDetails.bedroom_number,
  //     capacity: listingDetails.capacity,
  //     guesting_capacity: listingDetails.capacity,
  //     neighborhood: listingDetails.area_name,
  //     region: 'Massachusetts',
  //     city: 'Nantucket',
  //     country: 'United States',
  //     url: document.URL,
  //     referrer: document.referrer,
  //   });
  // }, [listingDetails]);

  return (
    <RentalDetailsContext.Provider
      value={{
        checkoutRef,
        propertyInfoRef,
        locationRef,
        availabilityRef,
        openContactAgentForm,
        setOpenContactAgentForm,
        openBookingForm,
        setOpenBookingForm,
      }}
    >
      {children}
    </RentalDetailsContext.Provider>
  );
};

export default RentalDetailsContextContainer;
