'use client';

import { useContext, useState } from 'react';

import {
  IMAGE_COUNT_LABEL_ID,
  VIEW_ALL_PHOTOS_ID,
  VIRTUAL_TOUR_ID,
} from '@/app/components/rental-listing-details/MediaView';
import { RentalDetailsContext } from '@/clients/contexts/RentalDetailsContext';
import { IListingImage } from '@/types/rental-listing-details';

import dynamic from 'next/dynamic';

import VirtualTourModal from '../rental-listing-details/VirtualTourModal';

const MediaViewCarousel = dynamic(() => import('../rental-listing-details/MediaViewCarousel'), {
  ssr: false,
});

const MediaViewWrapper = ({
  children,
  images,
  isMobile,
  virtual_tour_link,
}: {
  children: React.ReactNode;
  images: IListingImage[];
  isMobile?: boolean;
  virtual_tour_link?: string;
}) => {
  const { propertyInfoRef } = useContext(RentalDetailsContext);
  const [showMediaView, setShowMediaView] = useState<boolean>(false);
  const [showVirtualTour, setShowVirtualTour] = useState<boolean>(false);
  return (
    <div className='w-full'>
      <div
        ref={propertyInfoRef}
        onClick={(e: any) => {
          if (
            (e.target.tagName === 'IMG' ||
              e.target.id === VIEW_ALL_PHOTOS_ID ||
              e.target.id === IMAGE_COUNT_LABEL_ID) &&
            !isMobile
          ) {
            setShowMediaView(true);
          }

          if (e.target.id === VIRTUAL_TOUR_ID && !isMobile) {
            setShowVirtualTour(true);
          }
        }}
      >
        {children}
      </div>
      {showMediaView && (
        <MediaViewCarousel
          open={showMediaView}
          images={images}
          onClose={() => setShowMediaView(false)}
        />
      )}
      {showVirtualTour && (
        <VirtualTourModal
          open={showVirtualTour}
          onClose={() => setShowVirtualTour(false)}
          virtual_tour_link={virtual_tour_link ?? ''}
        />
      )}
    </div>
  );
};

export default MediaViewWrapper;
