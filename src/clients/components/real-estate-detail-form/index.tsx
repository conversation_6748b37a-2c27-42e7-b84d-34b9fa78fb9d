'use client';

import React, { useCallback, useState } from 'react';

import useForm from '@/clients/hooks/useForm';
import Button from '@/clients/ui/button';
import { Input } from '@/clients/ui/input';
import { EMAIL_PATTERN, PHONE_NUMBER_PATTERN } from '@/constants/patterns';
import { SegmentEvents } from '@/types/analytics';
import { ProgressStatus } from '@/types/common';

const BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || '';

type ContactAgentPayload = {
  comment: string;
  email: string;
  first_name: string;
  last_name: string;
  phone: string;
  agent_name?: string;
  contact_method?: string;
  neighborhood?: string;
  property_address?: string;
};

export const contactAgent = async (data: ContactAgentPayload) => {
  const res = await fetch(`${BASE_URL}/contact-agent`, {
    method: 'POST',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });

  if (!res.ok) {
    throw new Error('Failed to Submit Contact Agent Request');
  }

  return res.json();
};

const ContactAgentForm = ({ property_address, neighborhood, agent }: Record<string, string>) => {
  const [progressStatus, setProgressStatus] = React.useState<ProgressStatus | null>(null);

  const { formState, onResetForm, errors, onChange, preSubmitCheck } = useForm<ContactAgentPayload>(
    {
      first_name: '',
      email: '',
      phone: '',
      last_name: '',
      comment: '',
    },
    {
      first_name: (_v, _n, _value) => {
        if (_value.trim().length === 0) {
          return `Name is required.`;
        }
      },
      last_name: (_v, _n, _value) => {
        if (_value.trim().length === 0) {
          return `Last name is required.`;
        }
      },
      email: (_v, _n, _value: string) => {
        if (_value.trim().length === 0) {
          return `Email is required.`;
        }

        if (!_value.match(EMAIL_PATTERN)) {
          return 'Invalid email address';
        }
      },
      phone: (_v, _n, _value: string) => {
        if (_value.trim().length === 0) {
          return `Phone number is required.`;
        }

        if (!_value.match(PHONE_NUMBER_PATTERN)) {
          return 'Invalid phone number';
        }
      },
      comment: (_v, _n, _value: string) => {
        if (_value.trim().length === 0) {
          return `Message is required.`;
        }
      },
    },
  );

  const onSubmit = useCallback(
    (e: React.ChangeEvent<HTMLFormElement>) => {
      e.preventDefault();
      setProgressStatus(ProgressStatus.LOADING);
      const _errors = preSubmitCheck();
      if (Object.values(_errors).some((_error) => _error !== '')) {
        return;
      }
      contactAgent({
        email: formState.email,
        first_name: formState.first_name,
        last_name: formState.last_name,
        phone: formState.phone,
        agent_name: agent,
        comment: formState.comment,
        contact_method: 'phone',
        neighborhood: neighborhood,
        property_address: property_address,
      })
        .then((data) => {
          if (data.state === 'success') {
            onResetForm();
            setProgressStatus(ProgressStatus.SUCCESSFUL);
            window?.analytics?.track(SegmentEvents.SELL_FORM_SUBMITTED, {
              email: formState.email,
              first_name: formState.first_name,
              last_name: formState.last_name,
              how_can_we_help: formState.comment,
              phone: formState.phone,
              neighborhood,
              property_address,
              listing_agent_name: agent,
              url: document.URL,
              referrer: document.referrer,
            });
          }
        })
        .catch(() => {
          setProgressStatus(ProgressStatus.FAILED);
        });
    },
    [preSubmitCheck, formState, agent, neighborhood, property_address, onResetForm],
  );

  const onChangeTextInput = useCallback(
    (event: any) => {
      const { name, value } = event.target;
      onChange(value, name);
    },
    [onChange],
  );

  return (
    <>
      <form onSubmit={onSubmit}>
        <div>
          <Input
            className='px-2.5 py-4 w-full border rounded mb-3 text-gray-80 md:w-96'
            type='text'
            placeholder='First name'
            name='first_name'
            helperText={errors?.first_name ?? ''}
            error={!!errors?.first_name?.length}
            onChange={onChangeTextInput}
          />
        </div>
        <div>
          <Input
            className='px-2.5 py-4 w-full border rounded mb-3 text-gray-80 md:w-96'
            type='text'
            name='last_name'
            placeholder='Last name'
            helperText={errors?.last_name ?? ''}
            error={!!errors?.last_name?.length}
            onChange={onChangeTextInput}
          />
        </div>
        <div>
          <Input
            className='px-2.5 py-4 w-full border rounded mb-3 text-gray-80 md:w-96'
            type='text'
            placeholder='Phone'
            name='phone'
            helperText={errors?.phone ?? ''}
            error={!!errors?.phone?.length}
            onChange={onChangeTextInput}
          />
        </div>
        <div>
          <Input
            className='px-2.5 py-4 w-full border rounded mb-3 text-gray-80 md:w-96'
            type='text'
            placeholder='Email'
            name='email'
            helperText={errors?.email ?? ''}
            error={!!errors?.email?.length}
            onChange={onChangeTextInput}
          />
        </div>
        <div>
          <Input
            className='px-2.5 py-4 w-full border rounded mb-3 text-gray-80 md:w-96'
            type='text'
            placeholder={'I am interested in ' + property_address}
            name='comment'
            helperText={errors?.comment ?? ''}
            error={!!errors?.comment?.length}
            onChange={onChangeTextInput}
          />
        </div>
        <Button title='Contact Agent' className='w-full py-2 md:w-96' isSubmit />
      </form>
      <div>
        <p className='text-sm mt-4'>
          <span>{progressStatus === ProgressStatus.LOADING ? 'Loading...' : ''}</span>
          <span>
            {progressStatus === ProgressStatus.SUCCESSFUL ? 'Form submitted successfully.' : ''}
          </span>
          <span>
            {progressStatus === ProgressStatus.FAILED
              ? 'Something went wrong. Please try again later.'
              : ''}
          </span>
        </p>
      </div>
    </>
  );
};

export default ContactAgentForm;
