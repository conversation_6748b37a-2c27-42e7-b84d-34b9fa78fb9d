'use client';

import { memo } from 'react';

import Button from '@/clients/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogOverlay,
  DialogTitle,
} from '@/clients/ui/dialog';
import { cn } from '@/lib/utils';
import { Nullable, ProgressStatus } from '@/types/common';

type Props = {
  onClose: () => void;
  onConfirm: () => void;
  closeButtonText?: string;
  confirmButtonText?: string;
  title?: string;
};

const ConfirmDialog = ({
  onClose,
  onConfirm,
  confirmButtonText = 'Confirm',
  closeButtonText = 'Close',
  title,
}: Props) => {
  return (
    <Dialog open>
      <DialogOverlay className='bg-black/70 z-[99]' />
      <DialogContent
        hideCloseButton
        className={cn(
          'h-[130px] p-2 md:p-4 w-max',
          'rounded-xl shadow-md bg-white dark:bg-zinc-900 z-[99]',
        )}
        onInteractOutside={onClose}
      >
        <DialogTitle className='hidden' />
        <DialogDescription className='hidden' />
        <p>{title ?? 'Are you sure you want to continue?'}</p>
        <div className='flex items-center justify-between'>
          <Button
            intent='secondary'
            className='rounded-lg text-sm font-normal border-black'
            onClick={onClose}
          >
            {closeButtonText}
          </Button>
          <Button className='rounded-lg text-sm font-normal' onClick={onConfirm}>
            {confirmButtonText}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default memo(ConfirmDialog);
