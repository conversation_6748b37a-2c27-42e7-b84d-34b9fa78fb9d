'use client';

import { useMemo } from 'react';

import { DateRange, DayContentProps } from 'react-day-picker';

import { Nullable } from '@/types/common';
import { IListingRate } from '@/types/rental-listing-details';

import classNames from 'classnames';
import { isBefore, isSameDay, startOfDay } from 'date-fns';

type Props = {
  selected?: DateRange;
  rentalRateForDate?: IListingRate;
  blockedStartDates: Date[];
  blockedEndDates: Date[];
  checkinDay: Nullable<string>;
  isForcedCheckinDay?: Nullable<boolean>;
  popUpInfo: Nullable<{
    dt: Date;
    text: string;
  }>;
} & DayContentProps;

const RDPCustomDate = ({
  date,
  selected,
  rentalRateForDate,
  blockedStartDates,
  blockedEndDates,
  popUpInfo,
  checkinDay,
  isForcedCheckinDay,
}: Props) => {
  const showPopupInfo = useMemo(
    () => popUpInfo && isSameDay(popUpInfo.dt, date),
    [date, popUpInfo],
  );
  const isBlockStartDate = useMemo(
    () => blockedStartDates.some((_d) => isSameDay(_d, date)),
    [blockedStartDates, date],
  );

  const isBlockEndDate = useMemo(
    () => blockedEndDates.some((_d) => isSameDay(_d, date)),
    [blockedEndDates, date],
  );
  return (
    <>
      <span
        className={classNames('relative', {
          'font-normal no-checkin':
            !!checkinDay &&
            !rentalRateForDate?.allow_checkin &&
            !isBefore(startOfDay(date), startOfDay(new Date())) &&
            !(rentalRateForDate?.allow_checkout && selected?.from),
          blockedBoundary: isBlockStartDate || isBlockEndDate,
          'forced_checkin_day !font-bold': isForcedCheckinDay,
        })}
        role='presentation'
      >
        {date.getDate()}
        {isSameDay(startOfDay(date), startOfDay(new Date())) && (
          <span className='w-1 h-1 rounded-full bg-[#264646] absolute -bottom-1.5 left-0 right-0 mx-auto' />
        )}
      </span>
      {showPopupInfo && (
        <span className='absolute px-2 py-1 bg-black rounded-sm z-[99] text-xs font-normal -top-8 text-white w-max'>
          {popUpInfo?.text}
        </span>
      )}
    </>
  );
};

export default RDPCustomDate;
