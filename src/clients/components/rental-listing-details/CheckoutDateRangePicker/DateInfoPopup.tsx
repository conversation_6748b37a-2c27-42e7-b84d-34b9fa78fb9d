'use client';

import { useEffect, useState } from 'react';

import { DatePickerInfoPopUp } from './CheckoutDateRangePickerMobile';

type Props = {
  popUpInfo?: DatePickerInfoPopUp;
  setPopUpInfo?: (_i: DatePickerInfoPopUp) => void;
};

const DateInfoPopup = ({ popUpInfo, setPopUpInfo }: Props) => {
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(false);
      setPopUpInfo?.({
        bottom: undefined,
        right: undefined,
        left: undefined,
      });
    }, 1000);

    // Cleanup the timer when the component is unmounted
    return () => clearTimeout(timer);
  }, [setPopUpInfo]);

  if (!isVisible) {
    return null; // Render nothing when unmounted
  }

  return (
    <div
      className='min-w-[150px] absolute px-2 py-1 bg-white rounded-sm border border-solid border-platinium z-10 text-sm'
      style={
        popUpInfo?.right
          ? {
              right: popUpInfo?.right,
              bottom: popUpInfo?.bottom,
            }
          : {
              bottom: popUpInfo?.bottom,
              left: popUpInfo?.left,
            }
      }
    >
      {popUpInfo?.text ?? ''}
    </div>
  );
};

export default DateInfoPopup;
