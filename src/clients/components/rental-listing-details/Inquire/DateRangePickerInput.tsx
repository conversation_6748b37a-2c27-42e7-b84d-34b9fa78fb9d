'use client';

import { ComponentProps, useCallback, useEffect, useState } from 'react';

import { ChevronDownIcon } from '@heroicons/react/24/outline';
import { DateRange } from 'react-day-picker';

import InputLabel from '@/app/ui/input-label';
import LoadingSpinner from '@/app/ui/loading-spinner';
import { Popover, PopoverContent, PopoverTrigger } from '@/clients/ui/popover';
import { IListingDetails } from '@/types/rental-listing-details';

import { format } from 'date-fns';
import dynamic from 'next/dynamic';

const CheckoutDateRangePicker = dynamic(() => import('../CheckoutDateRangePicker/index'), {
  loading: () => (
    <div className='md:min-w-[570px] md:min-h-[468px] flex items-center justify-center'>
      <LoadingSpinner />
    </div>
  ),
});

type Props = {
  date?: DateRange;
  setDate: (_d?: DateRange) => void;
  propertyDetails: IListingDetails;
} & ComponentProps<typeof PopoverContent>;

const DateRangePickerInput = ({ date, setDate, propertyDetails, ...rest }: Props) => {
  const [open, setOpen] = useState<boolean>(false);

  const onClear = useCallback(() => {
    setDate(undefined);
  }, [setDate]);

  const onClose = useCallback(() => {
    setOpen(false);
  }, []);

  const onClickedTrigger = useCallback((e: any) => {
    e.preventDefault();
  }, []);

  const onClick = useCallback(() => {
    setOpen(true);
  }, []);

  useEffect(() => {
    if (date?.from && date?.to) {
      onClose();
    }
  }, [date?.from, date?.to, onClose]);

  return (
    <>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild onClick={onClickedTrigger}>
          <div className='flex items-center gap-x-2'>
            <div className='w-1/2'>
              <InputLabel className=''>CHECK IN</InputLabel>
              <div
                onClick={onClick}
                onKeyDown={onClick}
                role='button'
                tabIndex={0}
                className='border border-solid border-platinium flex items-center gap-x-2 py-2.5 px-3.5 text-sm  text-metal-gray rounded-[40px] relative cursor-pointer'
              >
                {date?.from ? format(date?.from, 'LLL d, yyyy') : 'Add Date'}
                <ChevronDownIcon className='w-4 h-4 text-foundation-blue absolute right-4' />
              </div>
            </div>
            <div className='w-1/2'>
              <InputLabel className=''>CHECK OUT</InputLabel>
              <div
                onClick={onClick}
                onKeyDown={onClick}
                role='button'
                tabIndex={0}
                className='border border-solid border-platinium flex items-center gap-x-2 py-2.5 px-3.5 text-sm  text-metal-gray rounded-[40px] relative cursor-pointer'
              >
                {date?.to ? format(date?.to, 'LLL d, yyyy') : 'Add Date'}
                <ChevronDownIcon className='w-4 h-4 text-foundation-blue absolute right-4' />
              </div>
            </div>
          </div>
        </PopoverTrigger>

        {open && (
          <PopoverContent
            className='md:min-w-[554px] w-max border border-solid border-english-manor border-opacity-40 p-6'
            side='top'
            align='end'
            sideOffset={-100}
            avoidCollisions={false}
            {...rest}
          >
            <CheckoutDateRangePicker
              onClear={onClear}
              onClose={onClose}
              date={date}
              setDate={setDate}
              availableCalendar={propertyDetails.availabilities}
              rentalRates={propertyDetails.rates}
              propertyId={propertyDetails.listing_id}
            />
          </PopoverContent>
        )}
      </Popover>
    </>
  );
};

export default DateRangePickerInput;
