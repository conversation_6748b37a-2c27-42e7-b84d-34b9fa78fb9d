'use client';

import { use<PERSON><PERSON>back, useMemo, useState } from 'react';

import { XMarkIcon } from '@heroicons/react/24/outline';
import { DateRange } from 'react-day-picker';
import toast from 'react-hot-toast';

import { submitBookingRequest } from '@/app/services/rental-listing-details';
import FormHelperText from '@/app/ui/form-helper-text';
import InputLabel from '@/app/ui/input-label';
import ResponsiveDialog from '@/clients/components/common/ResponsiveDialog';
import { PetType } from '@/clients/contexts/BookingContext';
import useForm from '@/clients/hooks/useForm';
import Button from '@/clients/ui/button';
import Checkbox from '@/clients/ui/checkbox';
import { Input } from '@/clients/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/clients/ui/select';
import { Separator } from '@/clients/ui/separator';
import Textarea from '@/clients/ui/textarea';
import { EMAIL_PATTERN, PHONE_NUMBER_PATTERN } from '@/constants/patterns';
import { SegmentEvents } from '@/types/analytics';
import { IListingDetails } from '@/types/rental-listing-details';
import { Nullable } from '@/utils/common';

import classNames from 'classnames';
import dayjs from 'dayjs';
import Image from 'next/image';

import GuestAndPetSelector, { GuestsValues } from '../GuestAndPetSelector';

import DateRangePickerInput from './DateRangePickerInput';
import DateRangePickerMobile from './DateRangePickerMobile';

type Props = {
  listingDetails: IListingDetails;
  onToggle: () => void;
};

export type AskQuestionFormValues = {
  firstname: string;
  lastname: string;
  email: string;
  phone: string;
  message: string;
};

const InquireDialog = ({ listingDetails, onToggle }: Props) => {
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [petCount, setPetCount] = useState<number>(1);
  const [petType, setPetType] = useState<PetType>(PetType.DOG);
  const [petDescription, setPetDescription] = useState<string>('');
  const [isPetSelected, setIsPetSelected] = useState<boolean>(false);
  const [flexibility, setFlexibility] = useState<Nullable<string>>(null);
  const [guestsValues, setGuestsValues] = useState<GuestsValues>({
    adults: 1,
    children: 0,
  });
  const [date, setDate] = useState<DateRange | undefined>(undefined);
  const petsAllowed = useMemo(
    () => listingDetails?.requirement?.pet_allow?.toLowerCase() === 'true',
    [listingDetails?.requirement?.pet_allow],
  );
  const {
    formState,
    errors,
    onChange,
    preSubmitCheck: preSubmitCheck,
  } = useForm<AskQuestionFormValues>(
    {
      firstname: '',
      lastname: '',
      email: '',
      phone: '',
      message: '',
    },
    {
      firstname: (_v, _n, _value: string) => {
        if (_value.trim().length === 0) {
          return `Please enter a first name`;
        }
      },
      lastname: (_v, _n, _value: string) => {
        if (_value.trim().length === 0) {
          return `Please enter a last name`;
        }
      },
      email: (_v, _n, _value: string) => {
        if (_value.trim().length === 0) {
          return `Please enter an email`;
        }

        if (!_value.match(EMAIL_PATTERN)) {
          return 'Invalid email address';
        }
      },
      phone: (_v, _n, _value: string) => {
        if (_value.trim().length === 0) {
          return `Please enter phone number`;
        }

        if (!_value.match(PHONE_NUMBER_PATTERN)) {
          return 'Invalid phone number';
        }
      },
      message: (_v, _n, _value: string) => {
        if (_value.trim().length === 0) {
          return `Please enter message`;
        }
      },
    },
  );

  const onChangeTextInput = useCallback(
    (event: any) => {
      const { name, value } = event.target;

      onChange(value, name);
    },
    [onChange],
  );

  const dateRangePickerError = useMemo(
    () => (!date?.from || !date?.to ? 'Please select dates' : null),
    [date?.from, date?.to],
  );

  const onSelectFlexibility = useCallback(
    (value: string) => {
      setFlexibility(value);
    },
    [setFlexibility],
  );

  const onSubmit = useCallback(() => {
    const _errors = preSubmitCheck();
    if (Object.values(_errors).some((_error) => _error !== '') || !!dateRangePickerError) {
      return;
    }

    if (!date?.from || !date?.to) {
      return;
    }
    setSubmitting(true);
    const hasValidDates = date && dayjs(date.from).isValid() && dayjs(date.to).isValid();

    submitBookingRequest({
      email: formState.email ?? '',
      phone: formState.phone ?? '',
      first_name: formState.firstname ?? '',
      last_name: formState.lastname ?? '',
      comment: formState.message ?? '',
      neighborhood: listingDetails?.area_name ?? '',
      listing_id: listingDetails?.listing_id,
      guest: guestsValues.adults ?? 1,
      children: guestsValues?.children ?? 0,
      property_address: listingDetails?.address,
      interest: 'rentals',
      arrival_date: hasValidDates ? dayjs(date.from).format('YYYY-MM-DD') : undefined,
      departure_date: hasValidDates ? dayjs(date.to).format('YYYY-MM-DD') : undefined,
      pet_count: isPetSelected ? petCount : undefined,
      pet_type: isPetSelected ? petType : undefined,
      pet_description: isPetSelected ? petDescription : undefined,
      contact_method: 'email',
      flexibility,
    })
      .then(() => {
        setSubmitting(false);
        window?.analytics?.track(SegmentEvents.BOOKING_REQUESTED, {
          amenities: listingDetails.featured_amenities,
          capacity: listingDetails.capacity,
          checkin_date: hasValidDates ? dayjs(date.from).format('YYYY-MM-DD') : null,
          checkout_date: hasValidDates ? dayjs(date.to).format('YYYY-MM-DD') : null,
          listing_id: listingDetails.listing_id,
          listing_name: listingDetails.address,
          listing_number_of_bedrooms: listingDetails.bedroom_number,
          neighborhood: listingDetails.area_name,
          num_adults: guestsValues?.adults ?? 1,
          num_children: guestsValues?.children ?? 0,
          number_of_days: dayjs(date.to).diff(date.from, 'days'),
          number_of_guests: Number(guestsValues?.adults ?? 1) + Number(guestsValues?.children ?? 0),
          price: listingDetails.peak_rate,
          region: 'Massachusetts',
          city: 'Nantucket',
          country: 'United States',
          guest: guestsValues.adults ?? 1,
          children: guestsValues?.children ?? 0,
          arrival_date: hasValidDates ? dayjs(date.from).format('YYYY-MM-DD') : undefined,
          departure_date: hasValidDates ? dayjs(date.to).format('YYYY-MM-DD') : undefined,
          pet_count: isPetSelected ? petCount : undefined,
          pet_type: isPetSelected ? petType : undefined,
          pet_description: isPetSelected ? petDescription : undefined,
          url: document.URL,
          referrer: document.referrer,
        });
        toast.success('Your request has been sent successfully');
        onToggle();
      })
      .catch((e) => {
        console.log(e);
        setSubmitting(false);
      });
  }, [
    preSubmitCheck,
    dateRangePickerError,
    date,
    formState.email,
    formState.phone,
    formState.firstname,
    formState.lastname,
    formState.message,
    listingDetails.area_name,
    listingDetails.listing_id,
    listingDetails.address,
    listingDetails.featured_amenities,
    listingDetails.capacity,
    listingDetails.bedroom_number,
    listingDetails.peak_rate,
    guestsValues.adults,
    guestsValues?.children,
    isPetSelected,
    petCount,
    petType,
    petDescription,
    flexibility,
    onToggle,
  ]);

  return (
    <ResponsiveDialog
      open
      onOpenChange={onToggle}
      dialogClassName='p-0 md:h-auto md:w-[480px] gap-0'
      drawerClassName='overflow-y-hidden z-[66] bg-white'
      drawerContentClassName='!p-0'
      hideCloseButton
    >
      <>
        <XMarkIcon
          className='hidden md:block w-8 h-8 absolute -top-3 -right-3 bg-white rounded-full shadow-sm cursor-pointer p-2'
          onClick={onToggle}
        />
        <div className='p-4'>
          <p className='m-0 font-semibold'>Inquire about this property</p>
        </div>
        <Separator />
        <div className='h-[calc(90dvh-132px)] md:h-[620px] overflow-x-hidden overflow-y-scroll'>
          <div className='flex gap-x-3 p-4'>
            <Image
              alt='listing image'
              src={listingDetails.images[0]?.url}
              width={0}
              height={0}
              className='w-[150px] h-auto max-h-[116px] rounded-md object-cover object-center'
              sizes='200px'
              placeholder='blur'
              blurDataURL='https://via.placeholder.com/150'
            />
            <div>
              <p className='m-0 text-sm font-medium'>{listingDetails.headline}</p>
              <div className='flex items-center gap-x-8 my-2.5'>
                <Image
                  alt='NR logo'
                  src='/images/nr-logo.svg'
                  width={0}
                  height={0}
                  className='w-[100px] h-auto'
                />
                <span className='text-xs text-right'>by</span>
              </div>
              <Image
                alt='CNC logo'
                src='/images/cc-logo.svg'
                width={0}
                height={0}
                className='w-[150px] h-auto'
              />
            </div>
          </div>
          <Separator className='my-2' />
          <div className='p-4'>
            <p className='text-base font-medium leading-[21px] tracking-[-0.35px] m-0 mb-4'>
              About your trip
            </p>
            <GuestAndPetSelector
              guestsValues={guestsValues}
              setGuestsValues={setGuestsValues}
              petCount={petCount}
              setPetCount={setPetCount}
              isPetSelected={isPetSelected}
              petsAllowed={petsAllowed}
              capacity={listingDetails.capacity}
              petType={petType}
              setPetType={setPetType}
              petDescription={petDescription}
              setPetDescription={setPetDescription}
              setIsPetSelected={setIsPetSelected}
            />
            <div className='my-4'>
              <div className='hidden md:block'>
                <DateRangePickerInput
                  date={date}
                  setDate={setDate}
                  propertyDetails={listingDetails}
                />
              </div>
              <DateRangePickerMobile
                date={date}
                setDate={setDate}
                propertyDetails={listingDetails}
              />
              {dateRangePickerError && (
                <div className='ml-2'>
                  <FormHelperText error>{dateRangePickerError}</FormHelperText>
                </div>
              )}
            </div>

            <InputLabel className=''>FLEXIBILITY</InputLabel>
            <Select value={flexibility ?? ''} onValueChange={onSelectFlexibility}>
              <SelectTrigger className='w-full rounded-[40px] h-[42px]'>
                <SelectValue placeholder='Select flexibility' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='exact_dates'>Exact dates only</SelectItem>
                <SelectItem value='flexible_dates'>I have some flexibility</SelectItem>
                <SelectItem value='uncertain'>Dates uncertain</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className='w-full p-4'>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-4 mb-4'>
              <div>
                <InputLabel className={classNames(!!errors?.firstname?.length && 'text-error')}>
                  First Name*
                </InputLabel>
                <Input
                  type='text'
                  name='firstname'
                  value={formState.firstname}
                  placeholder='Enter your first name'
                  className='w-full rounded-lg'
                  onChange={onChangeTextInput}
                  helperText={errors?.firstname ?? ''}
                  error={!!errors?.firstname?.length}
                />
              </div>
              <div>
                <InputLabel className={classNames(!!errors?.lastname?.length && 'text-error')}>
                  Last Name*
                </InputLabel>
                <Input
                  type='text'
                  name='lastname'
                  value={formState.lastname}
                  placeholder='Enter your last name'
                  className='w-full rounded-lg'
                  onChange={onChangeTextInput}
                  helperText={errors?.lastname ?? ''}
                  error={!!errors?.lastname?.length}
                />
              </div>
            </div>
            <div className='mb-4'>
              <p className='m-0 font-medium leading-[200%] tracking-[-0.6px]'>Your Details</p>
              <InputLabel className={classNames(!!errors?.email?.length && 'text-error')}>
                Email address*
              </InputLabel>
              <Input
                type='text'
                name='email'
                value={formState.email}
                placeholder='Enter your email'
                className='w-full rounded-lg'
                onChange={onChangeTextInput}
                helperText={errors?.email ?? ''}
                error={!!errors?.email?.length}
              />
            </div>
            <div className='mb-4'>
              <InputLabel className={classNames(!!errors?.phone?.length && 'text-error')}>
                Phone*
              </InputLabel>
              <Input
                type='text'
                name='phone'
                value={formState.phone}
                placeholder='Enter your phone number'
                className='w-full rounded-lg'
                onChange={onChangeTextInput}
                helperText={errors?.phone ?? ''}
                error={!!errors?.phone?.length}
              />
            </div>

            <InputLabel className={classNames(!!errors?.message?.length && 'text-error')}>
              Message*
            </InputLabel>
            <Textarea
              name='message'
              value={formState.message}
              placeholder='Enter your message'
              className='w-full rounded-lg h-[100px]'
              onChange={onChangeTextInput}
              helperText={errors?.message ?? ''}
              error={!!errors?.message?.length}
            />
            <div className='flex items-center w-full space-x-2.5 mt-4'>
              <Checkbox className='w-4 h-4' />
              <label htmlFor='ach' className='cursor-pointer text-xs md:text-sm'>
                Please send me new offers and updates by email.
              </label>
            </div>
          </div>
        </div>
        <div className='p-4 flex items-center gap-x-3'>
          <Button onClick={onToggle} intent='outline' className='rounded-full w-full font-medium'>
            Close
          </Button>
          <Button
            className='font-medium w-full rounded-full'
            onClick={onSubmit}
            disabled={submitting}
            isLoading={submitting}
          >
            Submit
          </Button>
        </div>
      </>
    </ResponsiveDialog>
  );
};

export default InquireDialog;
