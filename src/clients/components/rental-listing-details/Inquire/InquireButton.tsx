'use client';

import { ReactNode, useCallback, useContext, useState } from 'react';

import LoadingSpinner from '@/app/ui/loading-spinner';
import { useBooking } from '@/clients/contexts/BookingContext';
import { RentalDetailsContext } from '@/clients/contexts/RentalDetailsContext';
import Button from '@/clients/ui/button';
import { IListingDetails } from '@/types/rental-listing-details';

import dynamic from 'next/dynamic';
import { twMerge } from 'tailwind-merge';

const InquireDialog = dynamic(() => import('./InquireDialog'), {
  ssr: false,
  loading: () => (
    <div className='fixed bg-black/40 inset-0 flex items-center justify-center text-white'>
      <LoadingSpinner className='w-10 h-10' />
    </div>
  ),
});

type Props = {
  listingDetails: IListingDetails;
  className?: string;
  icon?: ReactNode;
  title?: string;
};

const InquireButton = ({ listingDetails, className = '', icon, title = 'Inquire' }: Props) => {
  const [show, setShow] = useState<boolean>(false);

  const onToggle = useCallback(() => {
    setShow((prev) => !prev);
  }, []);

  return (
    <>
      <Button
        intent='ghost'
        className={twMerge(
          'text-foundation-blue font-medium w-full py-4 hover:bg-transparent hover:text-carolina-blue-40',
          className,
        )}
        onClick={onToggle}
      >
        {icon}
        {title}
      </Button>
      {show && <InquireDialog listingDetails={listingDetails} onToggle={onToggle} />}
    </>
  );
};

export default InquireButton;
