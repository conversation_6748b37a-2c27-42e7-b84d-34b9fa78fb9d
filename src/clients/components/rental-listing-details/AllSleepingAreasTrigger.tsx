'use client';

import { useCallback, useState } from 'react';

import { H2 } from '@/app/ui/Typography';
import Button from '@/clients/ui/button';

const AllSleepingAreasTrigger = ({
  children,
  allSleepingAreasNode,
  showButton,
}: {
  children: React.ReactNode;
  allSleepingAreasNode: React.ReactNode;
  showButton?: boolean;
}) => {
  const [showAll, setShowAll] = useState<boolean>(false);

  const onToggle = useCallback(() => {
    setShowAll(!showAll);
  }, [showAll]);

  return (
    <>
      <div className='flex items-center justify-between mb-4 md:mb-6'>
        <H2 className='m-0'>Sleeping Arrangements</H2>
        {showButton && (
          <Button
            intent='ghost'
            onClick={onToggle}
            className='!text-[#4C737F] p-0 font-normal hover:text-foundation-blue hover:bg-transparent hidden md:block'
          >
            {!showAll ? `Show All` : `Close`}
          </Button>
        )}
      </div>
      <div className='grid grid-cols-1 gap-1'>{showAll ? allSleepingAreasNode : children}</div>
      {showButton && (
        <Button
          intent='ghost'
          onClick={onToggle}
          className='text-foundation-blue !p-0 font-normal hover:text-foundation-blue hover:bg-transparent md:hidden my-4'
        >
          {!showAll ? `Show All` : `Close`}
        </Button>
      )}
    </>
  );
};

export default AllSleepingAreasTrigger;
