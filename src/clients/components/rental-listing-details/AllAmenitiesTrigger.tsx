'use client';

import { useCallback, useState } from 'react';

import { H2 } from '@/app/ui/Typography';
import Button from '@/clients/ui/button';

const AllAmenitiesTrigger = ({
  children,
  allAmenitiesNode,
}: {
  children: React.ReactNode;
  allAmenitiesNode: React.ReactNode;
}) => {
  const [showAll, setShowAll] = useState<boolean>(false);

  const onToggle = useCallback(() => {
    setShowAll(!showAll);
  }, [showAll]);

  return (
    <>
      <div className='flex items-center justify-between mb-4 md:mb-6'>
        <H2 className='m-0'>Popular Amenities</H2>
        <Button
          intent='ghost'
          onClick={onToggle}
          className='!text-[#4C737F] p-0 font-normal hover:text-foundation-blue hover:bg-transparent hidden md:block'
        >
          {!showAll ? `Show All Amenities` : `Close`}
        </Button>
      </div>
      {children}
      {showAll && allAmenitiesNode}
      <Button
        intent='ghost'
        onClick={onToggle}
        className='!text-[#4C737F] !p-0 font-normal hover:text-foundation-blue hover:bg-transparent md:hidden my-4'
      >
        {!showAll ? `Show All Amenities` : `Close`}
      </Button>
    </>
  );
};

export default AllAmenitiesTrigger;
