import { XMarkIcon } from '@heroicons/react/20/solid';

import Modal from '@/clients/ui/modal';
import { IListingImage } from '@/types/rental-listing-details';

import Image from 'next/image';

type Props = {
  images: IListingImage[];
  open: boolean;
  onClose: () => void;
  propertyHeadline?: string;
};

const MobileViewAllPhotos = ({ open, onClose, images, propertyHeadline }: Props) => {
  return (
    <Modal open={open} onClose={onClose}>
      <div className='flex h-[100vh] flex-col'>
        <div className='flex min-h-[80px] w-full items-center justify-between shadow-header'>
          <p className='ml-4 text-lg font-medium'>{propertyHeadline}</p>
          <div className='z-[1] mr-4 h-6 w-6 cursor-pointer text-gray-main' onClick={onClose}>
            <XMarkIcon className='fill-current' />
          </div>
        </div>
        <div className='overflow-y-auto'>
          {images.map((_image) => (
            <div key={_image.image_uuid} className='my-4 shadow'>
              <Image
                alt={_image?.alt_text ?? ''}
                src={_image.url}
                width={0}
                height={0}
                className='h-[310px] w-full object-contain'
                sizes='100vw'
                placeholder='blur'
                blurDataURL='https://placehold.co/150'
              />
            </div>
          ))}
        </div>
      </div>
    </Modal>
  );
};

export default MobileViewAllPhotos;
