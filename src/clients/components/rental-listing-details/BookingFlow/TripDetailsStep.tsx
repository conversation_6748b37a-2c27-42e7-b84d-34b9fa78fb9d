'use client';

import { memo, useCallback } from 'react';

import { CalendarIcon } from '@heroicons/react/24/outline';

import { Separator } from '@/app/ui/separator';
import { BookingFlowStep, useBooking } from '@/clients/contexts/BookingContext';
import Button from '@/clients/ui/button';
import SvgGuests from '@/common/assets/svgs/Guests';
import { IListingDetails } from '@/types/rental-listing-details';
import { getStringSingularPlural } from '@/utils/common';

import { format } from 'date-fns';
import Image from 'next/image';

import EditTripDetailsButton from './EditTripDetailsButton';
import PriceDetails from './PriceDetails';

type Props = {
  data: IListingDetails;
};

const TripDetailsStep = ({ data }: Props) => {
  const { setStep, date, guests, petCount, isPetSelected } = useBooking();

  const onClickContinue = useCallback(() => {
    setStep(BookingFlowStep.REGISTER);
  }, [setStep]);

  return (
    <>
      <div className='px-4 py-4 md:py-5 h-max max-h-[calc(90dvh-136px)] overflow-x-hidden overflow-y-scroll md:overflow-y-auto pb-5 md:pb-0'>
        <div className='flex gap-x-3'>
          <Image
            alt='listing image'
            src={data.images[0]?.url}
            width={0}
            height={0}
            className='w-[150px] h-auto max-h-[116px] rounded-md object-cover object-center'
            sizes='200px'
            placeholder='blur'
            blurDataURL='https://via.placeholder.com/150'
          />
          <div>
            <p className='m-0 text-sm font-medium'>{data.headline}</p>
            <div className='flex items-center gap-x-8 my-2.5'>
              <Image
                alt='NR logo'
                src='/images/nr-logo.svg'
                width={0}
                height={0}
                className='w-[100px] h-auto'
              />
              <span className='text-xs text-right'>by</span>
            </div>
            <Image
              alt='CNC logo'
              src='/images/cc-logo.svg'
              width={0}
              height={0}
              className='w-[150px] h-auto'
            />
          </div>
        </div>
        <Separator className='mb-2' />
        <p className='text-sm m-0'>Step 1 of 3</p>
        <div className='px-4 py-2 border border-solid border-[#E2E8F0] rounded-2xl shadow-sm my-5'>
          <div className='flex items-center justify-between'>
            <p className='font-medium m-0'>About your trip</p>
            <EditTripDetailsButton text='Change' data={data} />
          </div>

          <div className='flex items-center gap-x-2 text-[#334155] my-3'>
            <CalendarIcon className='w-4 h-4' />
            <p className='text-xs m-0'>
              {date?.from && format(date?.from, 'EEE, LLL d')} -{' '}
              {date?.to && format(date?.to, 'EEE, LLL d, yyyy')}
            </p>
          </div>
          <div className='flex items-center gap-x-2 text-[#334155]'>
            <SvgGuests className='w-4 h-4' stroke='#334155' />
            <p className='text-xs m-0'>
              {getStringSingularPlural('Adult', 'Adults', guests.adults)},{' '}
              {getStringSingularPlural('Child', 'Children', guests.children)}
              {isPetSelected &&
                petCount > 0 &&
                `, ${getStringSingularPlural('Pet', 'Pets', petCount)}`}{' '}
            </p>
          </div>
        </div>

        <div className='px-4 py-2 border border-solid border-[#E2E8F0] rounded-2xl shadow-sm my-5'>
          <PriceDetails showDueToday />
        </div>

        <div className='px-4 py-2 border border-solid border-[#E2E8F0] rounded-2xl shadow-sm my-5'>
          <p className='text-[#047857] bg-[#D1FAE5] rounded-full shadow-sm w-max px-2 py-[2px] text-[10px] font-medium m-0'>
            Free cancellation
          </p>
          <p className='m-1.5 mb-0 text-xs'>
            Get 100% back if you cancel before confirmation. After confirmation, refund is issued if
            dates are rebooked.
          </p>
        </div>
      </div>
      <div className='p-4'>
        <Button className='w-full rounded-full px-5 py-3' onClick={onClickContinue}>
          Continue
        </Button>
      </div>
    </>
  );
};

export default memo(TripDetailsStep);
