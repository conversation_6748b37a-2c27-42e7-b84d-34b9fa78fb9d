'use client';

import { ComponentProps, useCallback, useState } from 'react';

import Button from '@/clients/ui/button';
import { IListingDetails } from '@/types/rental-listing-details';

import dynamic from 'next/dynamic';

const EditTripDetailsDialog = dynamic(() => import('./EditTripDetailsDialog'), { ssr: false });

type Props = {
  data: any;
  text?: string;
  defaultTab?: 'dates' | 'guests';
};

const EditTripDetailsButton = ({ data, text, defaultTab }: Props) => {
  const [open, setOpen] = useState<boolean>(false);

  const onToggle = useCallback(() => {
    setOpen((_o) => !_o);
  }, []);

  return (
    <>
      <Button
        onClick={onToggle as any}
        intent='ghost'
        className='text-primary-text text-sm underline !p-0 font-semibold'
      >
        {text ?? 'Edit'}
      </Button>
      {open && (
        <EditTripDetailsDialog
          onToggle={onToggle}
          data={data}
          defaultTab={defaultTab}
          petsAllowed={data?.requirement?.pet_allow?.toLowerCase() === 'true'}
        />
      )}
    </>
  );
};

export default EditTripDetailsButton;
