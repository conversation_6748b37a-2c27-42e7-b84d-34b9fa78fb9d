'use client';

import { memo, useCallback, useMemo, useState } from 'react';

import { XMarkIcon } from '@heroicons/react/24/outline';

import ConfirmDialog from '@/clients/components/common/ConfirmDialog';
import { BookingFlowStep, useBooking } from '@/clients/contexts/BookingContext';
import { IListingDetails } from '@/types/rental-listing-details';

import BillingDetailsStep from './BillingDetailsStep';
import BookingFlowHeader from './BookingFlowHeader';
import RegisterStep from './RegisterStep';
import TripDetailsStep from './TripDetailsStep';

type Props = {
  propertyDetails: IListingDetails;
};

const BookingFlowWrapper = ({ propertyDetails }: Props) => {
  const { step, setStep } = useBooking();
  const [showConfirm, setShowConfirm] = useState<boolean>(false);

  const currentScreen = useMemo(() => {
    switch (step) {
      case BookingFlowStep.DETAILS:
        return <TripDetailsStep data={propertyDetails} />;
      case BookingFlowStep.BILLING_DETAILS:
        return <BillingDetailsStep details={propertyDetails} />;
      case BookingFlowStep.REGISTER:
        return <RegisterStep />;
    }
  }, [propertyDetails, step]);

  const onClose = useCallback(() => {
    setShowConfirm((c) => !c);
  }, []);

  const onConfirm = useCallback(() => {
    setShowConfirm(false);
    setStep(null);
  }, [setStep]);

  const onBack = useCallback(() => {
    if (step === BookingFlowStep.BILLING_DETAILS) {
      setStep(BookingFlowStep.REGISTER);
    } else if (step === BookingFlowStep.DETAILS) {
      setShowConfirm(true);
    } else {
      setStep(BookingFlowStep.DETAILS);
    }
  }, [setStep, step]);

  return (
    <>
      <XMarkIcon
        className='hidden md:block w-8 h-8 absolute -top-3 -right-3 bg-white rounded-full shadow-sm cursor-pointer p-2 z-[999]'
        onClick={onClose}
      />
      <BookingFlowHeader onBack={onBack} />
      {currentScreen}
      {showConfirm && (
        <ConfirmDialog
          title='Are you sure you want to close?'
          onClose={onClose}
          onConfirm={onConfirm}
          confirmButtonText='Yes'
          closeButtonText='No'
        />
      )}
    </>
  );
};

export default memo(BookingFlowWrapper);
