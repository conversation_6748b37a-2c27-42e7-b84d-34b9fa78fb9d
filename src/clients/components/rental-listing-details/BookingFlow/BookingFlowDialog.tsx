'use client';

import { memo, useCallback, useMemo, useState } from 'react';

import { XMarkIcon } from '@heroicons/react/24/outline';

import ConfirmDialog from '@/clients/components/common/ConfirmDialog';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogOverlay,
  DialogTitle,
} from '@/components/ui/dialog';
import { BookingFlowStep, useBooking } from '@/contexts/BookingContext';
import { PropertyDetails } from '@/types/properties';

import BillingDetailsStep from './BillingDetailsStep';
import RegisterStep from './RegisterStep';
import TripDetailsStep from './TripDetailsStep';

type Props = {
  propertyDetails: PropertyDetails;
};

const BookingFlowDialog = ({ propertyDetails }: Props) => {
  const { step, setStep } = useBooking();
  const [showConfirm, setShowConfirm] = useState<boolean>(false);

  const currentScreen = useMemo(() => {
    switch (step) {
      case BookingFlowStep.DETAILS:
        return <TripDetailsStep data={propertyDetails} />;
      case BookingFlowStep.BILLING_DETAILS:
        return <BillingDetailsStep />;
      case BookingFlowStep.REGISTER:
        return <RegisterStep />;
    }
  }, [propertyDetails, step]);

  const onClose = useCallback(() => {
    setShowConfirm((c) => !c);
  }, []);

  const onConfirm = useCallback(() => {
    setShowConfirm(false);
    setStep(null);
  }, [setStep]);

  return (
    <>
      <Dialog open>
        <DialogOverlay className="bg-[rgba(217,217,217,0.90)]" />
        <DialogContent
          className="p-0 w-screen h-screen md:h-auto md:w-[380px]"
          hideCloseButton
          onInteractOutside={(e) => {
            e.preventDefault();
          }}
        >
          <DialogTitle className="hidden" />
          <DialogDescription className="hidden" />
          <XMarkIcon
            className="w-8 h-8 absolute -top-3 -right-3 bg-white rounded-full shadow-sm cursor-pointer p-2"
            onClick={onClose}
          />
          <div className="px-4 py-5">{currentScreen}</div>
        </DialogContent>
      </Dialog>
      {showConfirm && (
        <ConfirmDialog
          title="Are you sure you want to close?"
          onClose={onClose}
          onConfirm={onConfirm}
          confirmButtonText="Yes"
          closeButtonText="No"
        />
      )}
    </>
  );
};

export default memo(BookingFlowDialog);
