'use client';

import { memo, useCallback, useEffect, useRef, useState } from 'react';

import { XCircleIcon } from '@heroicons/react/24/outline';
import { DateRange } from 'react-day-picker';
import toast from 'react-hot-toast';

import { calculateRentForListing } from '@/app/services/rental-listing-details';
import { Separator } from '@/app/ui/separator';
import ResponsiveDialog from '@/clients/components/common/ResponsiveDialog';
import { useBooking } from '@/clients/contexts/BookingContext';
import Button from '@/clients/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/clients/ui/tabs';
import { Nullable } from '@/types/common';
import { IListingDetails, Rent } from '@/types/rental-listing-details';

import { format } from 'date-fns';

import CheckoutDateRangePicker from '../CheckoutDateRangePicker';
import CheckoutDateRangePickerMobile from '../CheckoutDateRangePicker/CheckoutDateRangePickerMobile';
import GuestSelectorComponent from '../GuestAndPetSelector/GuestAndPetSelectorComponent';

type Props = {
  onToggle: () => void;
  data: IListingDetails;
  petsAllowed?: boolean;
  defaultTab?: 'dates' | 'guests';
};

const EditTripDetailsDialog = ({ onToggle, data, petsAllowed, defaultTab = 'dates' }: Props) => {
  const {
    date: initialDate,
    setDate: setDateInContext,
    rentInfo: defaultRentInfo,
    guests,
    setGuests,
    petCount,
    setPetCount,
    petType,
    setPetType,
    petDescription,
    setPetDescription,
    isPetSelected,
    setIsPetSelected,
  } = useBooking();
  const [date, setDate] = useState<DateRange | undefined>(initialDate);
  const [isFetchingBookingDetails, setIsFetchingBookingDetails] = useState<boolean>(false);
  const [rentInfo, setRentInfo] = useState<Nullable<Rent>>(defaultRentInfo);

  const onClear = useCallback(() => {
    setDate(undefined);
    setRentInfo(null);
  }, []);

  const onSave = useCallback(() => {
    setDateInContext(date);
    onToggle();
  }, [date, onToggle, setDateInContext]);

  const dateRef = useRef(date);

  useEffect(() => {
    if (!date?.from || !date?.to) {
      setRentInfo(null);
      setIsFetchingBookingDetails(false);
      return;
    }

    dateRef.current = date;

    const NOT_AVAILABLE_MSG = 'Not available for given dates.';

    const fetchBookingAvailability = async (from: Date, to: Date) => {
      setIsFetchingBookingDetails(true);

      try {
        const _data = await calculateRentForListing<Rent>({
          arrival_date: format(from, 'yyyy-MM-dd'),
          departure_date: format(to, 'yyyy-MM-dd'),
          listing: data.listing_id,
        });

        if (!_data.rent) {
          const errorMsg = (_data as any)?.details ?? NOT_AVAILABLE_MSG;
          toast.error(errorMsg);
          setRentInfo(null);
          setIsFetchingBookingDetails(false);
          return;
        }

        if (dateRef.current?.from && dateRef.current?.to) {
          setRentInfo(_data);
          setIsFetchingBookingDetails(false);
        }
      } catch (error) {
        console.error(error);
        setRentInfo(null);
        setIsFetchingBookingDetails(false);
      }
    };

    const handler = setTimeout(() => {
      if (date?.from && date?.to) {
        fetchBookingAvailability(date.from, date.to);
      }
    }, 500); // debounce

    return () => clearTimeout(handler);
  }, [data.listing_id, date]);

  return (
    <ResponsiveDialog
      open={true}
      onOpenChange={onToggle}
      dialogClassName='md:min-w-[630px] md:max-w-max py-10 md:py-0'
      drawerClassName='h-[95dvh] overflow-y-hidden max-h-[95dvh]'
      drawerContentClassName='overflow-y-hidden'
      hideCloseButton
    >
      <div className='md:p-6'>
        <Button onClick={onToggle} intent='ghost' className='!p-0 absolute top-4 right-4'>
          <XCircleIcon className='w-6 h-6' />
        </Button>
        <p className='font-medium text-lg md:text-xl leading-[140%] tracking-[0.5px] m-0 text-center md:text-left'>
          Change reservation details
        </p>
        <Tabs defaultValue={defaultTab} className='w-full min-h-[500px]'>
          <TabsList className='w-full my-2 md:my-4'>
            <TabsTrigger value='dates' className='w-full cursor-pointer'>
              Dates
            </TabsTrigger>
            <TabsTrigger value='guests' className='w-full cursor-pointer'>
              Guests
            </TabsTrigger>
          </TabsList>
          <TabsContent className='mt-0 md:mt-2' value='dates'>
            <div className='hidden md:block'>
              <CheckoutDateRangePicker
                date={date}
                onClear={onClear}
                onClose={onToggle}
                setDate={setDate}
                availableCalendar={data.availabilities}
                rentalRates={data.rates}
                propertyId={data.listing_id}
                isFetchingBookingDetails={isFetchingBookingDetails}
                showSave
                onSave={onSave}
              />
            </div>
            <div className='md:hidden'>
              <CheckoutDateRangePickerMobile
                date={date}
                setDate={setDate}
                onClose={onToggle}
                availableCalendar={data.availabilities.sort(
                  (_a1, _a2) => new Date(_a1.to_date).getTime() - new Date(_a2.from_date).getTime(),
                )}
                rentalRates={data.rates}
                isFetchingBookingDetails={isFetchingBookingDetails}
                className='p-0'
                calendarWrapperClassName='h-[44dvh]'
                onSave={onSave}
                showSave
                hideClose
              />
            </div>
          </TabsContent>
          <TabsContent className='mt-0 md:mt-2 h-[70dvh] md:h-auto' value='guests'>
            <div className='h-full md:h-[400px] relative'>
              <GuestSelectorComponent
                petsAllowed={petsAllowed}
                capacity={data.capacity}
                guests={guests}
                setGuests={setGuests}
                petCount={petCount}
                setPetCount={setPetCount}
                petType={petType}
                setPetType={setPetType}
                petDescription={petDescription}
                setPetDescription={setPetDescription}
                isPetSelected={isPetSelected}
                setIsPetSelected={setIsPetSelected}
              />
              <p className='text-xs uppercase text-metal-gray'>
                This property has a maximum of {data.capacity} guests. <br />
                {!petsAllowed
                  ? `Pets are not allowed.`
                  : `Pets allowed with prior permission, fees may apply.`}
              </p>

              <div className='absolute w-full flex flex-col bottom-0'>
                <Separator className='my-4' />
                <Button
                  intent='outline'
                  className='text-sm font-normal rounded-[32px] text-right w-min self-end'
                  onClick={onToggle}
                >
                  Close
                </Button>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </ResponsiveDialog>
  );
};

export default memo(EditTripDetailsDialog);
