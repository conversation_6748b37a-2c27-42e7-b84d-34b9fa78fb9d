'use client';

import { memo } from 'react';

import { ArrowLeftIcon, PhoneIcon } from '@heroicons/react/24/outline';

type Props = {
  onBack?: () => void;
};

const BookingFlowHeader = ({ onBack }: Props) => {
  return (
    <div className='w-full relative flex items-center justify-between px-4 py-2.5  text-black text-xs bg-white rounded-lg'>
      <span className='flex items-center gap-x-2 md:absolute md:left-4 md:top-4'>
        <ArrowLeftIcon onClick={onBack} className='w-6 h-6 cursor-pointer' />
        Back
      </span>
      <a
        href='tel:************'
        className='md:hidden flex items-center justify-center px-3 py-[5px] border-[1px] border-solid border-[#BAE6FD] gap-x-2 rounded-full bg-white no-underline text-inherit '
      >
        <PhoneIcon className='w-5 h-5' />
        <span className='font-medium'>************</span>
      </a>
    </div>
  );
};

export default memo(BookingFlowHeader);
