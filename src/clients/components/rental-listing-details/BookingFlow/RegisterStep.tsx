'use client';

import { useCallback, useState } from 'react';

import FormHelperText from '@/app/ui/form-helper-text';
import Input<PERSON>abel from '@/app/ui/input-label';
import { BookingFlowStep, GuestData } from '@/clients/contexts/BookingContext';
import { useBooking } from '@/clients/contexts/BookingContext';
import useForm from '@/clients/hooks/useForm';
import Button from '@/clients/ui/button';
import Checkbox from '@/clients/ui/checkbox';
import { Input } from '@/clients/ui/input';
import { Separator } from '@/clients/ui/separator';
import { EMAIL_PATTERN } from '@/constants/patterns';

import classNames from 'classnames';

type Props = {};

const RegisterStep = () => {
  const { setStep, setGuestData } = useBooking();
  const {
    formState,
    pristine,
    errors,
    onChange,
    preSubmitCheck: preSubmitCheck,
  } = useForm<GuestData>(
    {
      firstname: '',
      lastname: '',
      phone: '',
      userLoginEmail: '',
    },
    {
      firstname: (_v, _n, _value: string) => {
        if (_value.trim().length === 0) {
          return `Please enter a first name`;
        }
      },
      lastname: (_v, _n, _value: string) => {
        if (_value.trim().length === 0) {
          return `Please enter a last name`;
        }
      },
      phone: (_v, _n, _value: string) => {
        if (_value.trim().length === 0) {
          return `Please enter a phone number`;
        }
      },
      userLoginEmail: (_v, _n, _value: string) => {
        if (_value.trim().length === 0) {
          return `Please enter an email`;
        }

        if (!_value.match(EMAIL_PATTERN)) {
          return 'Invalid email address';
        }
      },
    },
  );

  const onChangeTextInput = useCallback(
    (event: any) => {
      const { name, value } = event.target;

      onChange(value, name);
    },
    [onChange],
  );

  const onRegister = useCallback(() => {
    const _errors = preSubmitCheck();
    if (Object.values(_errors).some((_error) => _error !== '')) {
      return;
    }
    setGuestData(formState);
    setStep(BookingFlowStep.BILLING_DETAILS);
  }, [formState, preSubmitCheck, setGuestData, setStep]);

  return (
    <>
      <div className='px-4 py-4 md:py-5 h-max max-h-[calc(90dvh-136px)] overflow-x-hidden overflow-y-scroll md:overflow-y-auto pb-5 md:pb-0'>
        <p className='text-2xl font-semibold m-0 tracking-[-0.6px]'>Complete your reservation</p>
        <Separator className='my-2' />
        <p className='text-sm m-0 mb-4'>Step 2 of 3</p>
        <div className='mb-4'>
          <InputLabel
            className={classNames(
              'text-[#18181B] text-sm mb-2',
              (!!errors?.firstname?.length || !!errors?.lastname?.length) && 'text-error',
            )}
          >
            Guest Name
          </InputLabel>
          <div className='grid grid-cols-1 md:grid-cols-2 gap-x-3'>
            <Input
              type='text'
              name='firstname'
              value={formState.firstname}
              placeholder='First name'
              className='w-full px-2.5 py-3 rounded-lg'
              onChange={onChangeTextInput}
              error={!!errors?.firstname?.length}
            />
            <Input
              type='text'
              name='lastname'
              value={formState.lastname}
              placeholder='Last name'
              className='w-full px-2.5 py-3 rounded-lg'
              onChange={onChangeTextInput}
              error={!!errors?.lastname?.length}
            />
          </div>
          {!!errors?.firstname?.length && (
            <FormHelperText className='pl-2' error>
              {errors?.firstname ?? ''}
            </FormHelperText>
          )}
          {!!errors?.lastname?.length && (
            <FormHelperText className='pl-2' error>
              {errors?.lastname ?? ''}
            </FormHelperText>
          )}
        </div>
        <div className='mb-4'>
          <InputLabel
            className={classNames(
              'text-[#18181B] text-sm mb-2',
              !!errors?.phone?.length && 'text-error',
            )}
          >
            Phone number
          </InputLabel>
          <Input
            type='phone'
            name='phone'
            value={formState.phone}
            placeholder='Phone Number'
            className='w-full px-2.5 py-3 rounded-lg'
            pattern='\d*'
            inputMode='numeric'
            onChange={onChangeTextInput}
            helperText={errors?.phone ?? ''}
            error={!!errors?.phone?.length}
          />
          <p className='text-xs text-[#71717A] mt-2'>
            We&apos;ll use this to send important trip updates.
          </p>
        </div>
        <div className=''>
          <InputLabel
            className={classNames(
              'text-[#18181B] text-sm mb-2',
              !!errors?.userLoginEmail?.length && 'text-error',
            )}
          >
            Email address
          </InputLabel>
          <Input
            type='email'
            name='userLoginEmail'
            value={formState.userLoginEmail}
            placeholder='Enter your email address'
            className='w-full px-2.5 py-3 rounded-lg'
            onChange={onChangeTextInput}
            helperText={errors?.userLoginEmail ?? ''}
            error={!!errors?.userLoginEmail?.length}
          />
          <p className='text-xs text-[#71717A] mt-2'>
            We&apos;ll email you trip confirmation receipts.
          </p>
        </div>
        <div className='flex items-center space-x-1.5 mt-2'>
          <Checkbox id='travelInsurance' className='w-4 h-4' />
          <label htmlFor='ach' className='cursor-pointer text-xs text-[#71717A]'>
            Please send me news and marketing information
          </label>
        </div>
        <Separator className='my-4' />
      </div>
      <div className='p-4'>
        <Button className='font-medium w-full rounded-full px-4 py-3' onClick={onRegister}>
          Continue
        </Button>
      </div>
    </>
  );
};

export default RegisterStep;
