'use client';

import { memo, useMemo } from 'react';

import { QuestionMarkCircleIcon, XCircleIcon } from '@heroicons/react/24/outline';

import ResponsiveDialog from '@/clients/components/common/ResponsiveDialog';
import { useBooking } from '@/clients/contexts/BookingContext';
import Button from '@/clients/ui/button';
import { Separator } from '@/clients/ui/separator';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/clients/ui/tooltip';
import { currencyFormatter, getStringSingularPlural } from '@/utils/common';
import {
  calculateOwnerFee,
  calculateStateAndLocalTaxes,
  calculateTotalBeforeTaxes,
} from '@/utils/rentals';

import { differenceInCalendarDays } from 'date-fns';

import { getDiscountName } from '../PricesAndSummary';

type Props = {
  open: boolean;
  onToggle: () => void;
};

const PriceBreakdownDialog = ({ open, onToggle }: Props) => {
  const { rentInfo, isPetSelected, date, isInsuranceAdded } = useBooking();
  const numberOfNights = useMemo(
    () => (date?.from && date?.to ? differenceInCalendarDays(date.to, date.from) : 0),
    [date?.from, date?.to],
  );
  if (!rentInfo) {
    return null;
  }

  return (
    <ResponsiveDialog
      open={open}
      onOpenChange={onToggle}
      dialogClassName='p-4 w-[80vw] max-w-md mx-auto h-min max-h-[80dvh] rounded-xl'
      drawerContentClassName='pb-4'
    >
      <div className='relative'>
        <Button onClick={onToggle} intent='ghost' className='!p-0 absolute top-0 right-0 md:hidden'>
          <XCircleIcon className='w-6 h-6' />
        </Button>

        <p className='m-0 font-semibold'>Price breakdown</p>
        <Separator className='my-2' />

        <div className='overflow-auto max-h-[70dvh] md:max-h-none pb-4'>
          <div className='flex items-center justify-between mt-4'>
            <p className='m-0 text-xs font-medium'>
              {date?.from &&
                date?.to &&
                `${getStringSingularPlural('night', 'nights', numberOfNights)}`}
            </p>
            <p className='m-0 text-xs font-medium'>
              {currencyFormatter.format(rentInfo?.rent ?? 0)}
            </p>
          </div>

          {rentInfo?.discount_type && rentInfo?.discount && (
            <div className='flex items-center justify-between mt-3'>
              <p className='m-0 text-xs font-medium'>
                {getDiscountName(rentInfo?.discount_type ?? 'Discount Applied')}
              </p>
              <p className='m-0 text-xs font-medium text-green-600'>
                -{currencyFormatter.format(rentInfo?.discount ?? 0)}
              </p>
            </div>
          )}

          <div className='flex items-center justify-between mt-3'>
            <p className='m-0 text-xs font-medium flex items-center gap-x-1'>
              Nantucket Rentals Service Fee
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild className='cursor-pointer'>
                    <QuestionMarkCircleIcon className='w-4 h-4' />
                  </TooltipTrigger>
                  <TooltipContent className='max-w-[80dvw] md:max-w-[400px] z-[9999]'>
                    <p>
                      This fee helps Nantucket Rentals provide secure booking experiences, and offer
                      24/7 local support throughout your trip. This fee is also less than VRBO and
                      AIRBNB.
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </p>
            <p className='m-0 text-xs font-medium'>{currencyFormatter.format(rentInfo.fee ?? 0)}</p>
          </div>

          <div className='flex items-center justify-between mt-3'>
            <p className='m-0 text-xs font-medium'>State & Local Taxes</p>
            <p className='m-0 text-xs font-medium'>
              {rentInfo && currencyFormatter.format(calculateStateAndLocalTaxes(rentInfo))}
            </p>
          </div>

          <div className='flex items-center justify-between mt-3'>
            <p className='m-0 text-xs'>Other Fees</p>
            <p className='m-0 text-xs'>
              {rentInfo && currencyFormatter.format(calculateOwnerFee(rentInfo, isPetSelected))}
            </p>
          </div>

          {isPetSelected && (
            <div className='flex items-center justify-between mt-3'>
              <p className='m-0 text-xs'>Pet Fee</p>
              <p className='m-0 text-xs'>{currencyFormatter.format(rentInfo.pet_fee ?? 0)}</p>
            </div>
          )}

          {/* {isInsuranceAdded && (
            <div className='flex items-center justify-between mt-3'>
              <p className='m-0 text-xs'>Travel Insurance</p>
              <p className='m-0 text-xs'>
                {currencyFormatter.format(bookingAvailabilityData.travel_insurance_amount ?? 0)}
              </p>
            </div>
          )} */}

          <Separator className='my-2' />

          <div className='flex items-center justify-between mt-3'>
            <p className='m-0 text-sm font-semibold'>TOTAL (USD)</p>
            <p className='m-0 text-sm font-semibold'>
              {rentInfo &&
                currencyFormatter.format(calculateTotalBeforeTaxes(rentInfo, isPetSelected))}
            </p>
          </div>
        </div>
      </div>
    </ResponsiveDialog>
  );
};

export default memo(PriceBreakdownDialog);
