'use client';

import { useMemo } from 'react';

import { Separator } from '@/app/ui/separator';
import { useBooking } from '@/clients/contexts/BookingContext';
import { Popover, PopoverContent, PopoverTrigger } from '@/clients/ui/popover';
import { currencyFormatter, getStringSingularPlural } from '@/utils/common';
import { formatBookingFeesData } from '@/utils/rentals';

import { addDays, differenceInCalendarDays, format } from 'date-fns';

export const getDiscountName = (discountName = 'Discount Applied') => {
  if (discountName === 'Custom Discount') {
    return 'Discount Applied';
  } else {
    return discountName;
  }
};

const PricesAndSummary = () => {
  const { date, rentInfo, isPetSelected } = useBooking();

  const numberOfNights = useMemo(
    () => (date?.from && date?.to ? differenceInCalendarDays(date.to, date.from) : 0),
    [date?.from, date?.to],
  );

  const formattedPrices = useMemo(
    () => formatBookingFeesData(rentInfo, isPetSelected, numberOfNights),
    [isPetSelected, numberOfNights, rentInfo],
  );

  return (
    <div className='border border-solid border-platinium rounded-2xl p-3.5 my-4 md:my-0'>
      <div className='flex items-center justify-between mb-1 text-sm text-metal-gray'>
        <Popover>
          <PopoverTrigger asChild>
            <div className='underline cursor-pointer'>
              {date?.from &&
                date?.to &&
                `${getStringSingularPlural('night', 'nights', numberOfNights)}`}
            </div>
          </PopoverTrigger>
          <PopoverContent className='!shadow-card-25 p-0'>
            <>
              <div className='text-center font-semibold px-4 py-2'>Base Price Breakdown</div>
              <Separator className='' />
              <div className='px-4'>
                {[...Array(numberOfNights).keys()].map((_n, index) => (
                  <div
                    key={index}
                    className='my-2 flex items-center justify-between text-sm text-metal-gray'
                  >
                    <span>{date?.from ? format(addDays(date.from, _n), 'MM/dd/yyyy') : ''}</span>
                    <span>
                      {currencyFormatter.format(formattedPrices?.averageNightlyRate ?? 0)}
                    </span>
                  </div>
                ))}
              </div>
              <Separator className='' />
              <div className='px-4 py-2 flex items-center justify-between'>
                <span className='font-semibold'>Total Base Price</span>
                <span>
                  {currencyFormatter.format(
                    (formattedPrices?.averageNightlyRate ?? 0) * numberOfNights,
                  )}
                </span>
              </div>
            </>
          </PopoverContent>
        </Popover>

        <span className='text-base md:text-2xl font-medium text-black'>
          {currencyFormatter.format((formattedPrices?.averageNightlyRate ?? 0) * numberOfNights)}
        </span>
      </div>
      <div className='flex items-center justify-between mb-1 text-sm text-metal-gray'>
        <Popover>
          <PopoverTrigger asChild>
            <div className='underline cursor-pointer'>Booking Fee</div>
          </PopoverTrigger>
          <PopoverContent className='!shadow-card-25 text-sm text-metal-gray'>
            This fee helps Nantucket Rentals provide secure booking experiences, and offer 24/7
            local support throughout your trip. This fee is also less than VRBO and AIRBNB.
          </PopoverContent>
        </Popover>

        <span className='text-base md:text-2xl font-medium text-black'>
          {currencyFormatter.format(formattedPrices?.nantucketRentalsFee ?? 0)}
        </span>
      </div>
      <div className='flex items-center justify-between mb-1 text-sm text-metal-gray'>
        Other Fee
        <span className='text-base md:text-2xl font-medium text-black'>
          {currencyFormatter.format(formattedPrices?.other_fee ?? 0)}
        </span>
      </div>
      {isPetSelected && (
        <div className='flex items-center justify-between mb-1 text-sm text-metal-gray'>
          Pet Fee
          <span className='text-base md:text-2xl font-medium text-black'>
            {currencyFormatter.format(rentInfo?.pet_fee ?? 0)}
          </span>
        </div>
      )}
      <div className='flex items-center justify-between mb-1 text-sm text-metal-gray'>
        State & Local Taxes
        <span className='text-base md:text-2xl font-medium text-black'>
          {currencyFormatter.format(formattedPrices?.occupancyTax ?? 0)}
        </span>
      </div>
      {rentInfo?.discount_type && rentInfo?.discount && (
        <>
          <Separator className='my-2' />
          <div className='text-right line-through'>
            {currencyFormatter.format(formattedPrices?.totalBeforeDiscount ?? 0)}
          </div>
          <div className='flex items-center justify-between mb-1 text-sm text-metal-gray'>
            {getDiscountName(rentInfo?.discount_type ?? 'Discount Applied')}
            <span className='text-base md:text-2xl font-medium text-green-500'>
              -{currencyFormatter.format(rentInfo?.discount ?? 0)}
            </span>
          </div>
        </>
      )}
      <Separator className='my-2' />
      <div className='flex items-center justify-between text-sm text-metal-gray'>
        Total
        <span className='text-base md:text-2xl font-medium text-black'>
          {currencyFormatter.format(formattedPrices?.grandTotal ?? 0)}
        </span>
      </div>
    </div>
  );
};

export default PricesAndSummary;
