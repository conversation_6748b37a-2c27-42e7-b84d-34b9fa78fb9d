import React from 'react';

import SvgCsvUploaded from '@/common/assets/svgs/CsvUploaded';

const Approval = () => {
  return (
    <>
      <div className='text-center max-w-[846px] mx-auto'>
        <div className='mb-7.5'>
          <SvgCsvUploaded className='mx-auto' width={100} height={100} />
        </div>
        <p className='m-0 mb-5 text-lg md:text-xl'>Rental Agreement has been sent for approval</p>
        <div className='mb-7.5 text-[#6d7380]'>
          <p className='m-0 text-xs md:text-sm'>
            Your signed Rental Agreement and Payment have been received by Nantucket Rentals. When
            the Rental Agreement is approved and signed by the Homeowner, your payment will be
            processed and your booking will be confirmed.
          </p>
          <br />
          <p className='m-0 text-xs md:text-sm'>
            A copy of the fully signed agreement will be emailed to you.
          </p>
          <br />
          <p className='m-0 text-xs md:text-sm'>
            If the Homeowner wants to discuss your request you will be notified via email. The
            Messages tool will keep track of all your communication related to this booking and will
            remain open throughout your stay.
          </p>
        </div>
      </div>
    </>
  );
};

export default Approval;
