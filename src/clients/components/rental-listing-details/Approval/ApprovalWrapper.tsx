'use client';

import { ExclamationCircleIcon } from '@heroicons/react/24/outline';

import Link from 'next/link';
import { useSearchParams } from 'next/navigation';

import Approval from './Approval';

enum DocusignRedirectEvent {
  SIGNING_COMPLETED = 'signing_complete',
  EXPIRED = 'ttl_expired',
}

const ApprovalWrapper = () => {
  const searchParams = useSearchParams();
  const event = searchParams?.get('event');
  const propertyId = searchParams?.get('propertyId');

  return (
    <>
      {event === DocusignRedirectEvent.SIGNING_COMPLETED ? (
        <Approval />
      ) : (
        <>
          <div className='container py-[30px] sm:py-[40px] md:py-[100px] min-h-min'>
            <div className='text-center max-w-[846px] mx-auto'>
              <div className='my-5 flex flex-col items-center justify-center'>
                <ExclamationCircleIcon color='orange' className='w-10 h-10' />
                <p>Booking Failed!</p>
              </div>
              <div>
                <Link
                  href={`/${propertyId}`}
                  className='flex items-center justify-center m-auto bg-olive px-4 py-2 rounded-sm text-white no-underline w-[280px]'
                >
                  Go back to Property
                </Link>
              </div>
            </div>
          </div>
        </>
      )}
    </>
  );
};

export default ApprovalWrapper;
