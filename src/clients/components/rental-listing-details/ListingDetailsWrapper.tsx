'use client';

import { ReactNode, useCallback, useEffect } from 'react';

import ResponsiveDialog from '@/clients/components/common/ResponsiveDialog';
import { useBooking } from '@/clients/contexts/BookingContext';
import { ProgressStatus } from '@/types/common';
import { IListingDetails } from '@/types/rental-listing-details';

import { useRouter } from 'next/navigation';

import ProgressDialog from '../common/ProgressDialog';

import BookingFlowWrapper from './BookingFlow/BookingFlowWrapper';
import DocusignDialog from './BookingFlow/DocusignDialog';

type Props = {
  children: ReactNode;
  details: IListingDetails;
};

const ListingDetailsWrapper = ({ children, details }: Props) => {
  const router = useRouter();
  const {
    step: bookingStep,
    setStep: setBookingStep,
    docusignUrl,
    progressStatus,
    setDocusignUrl,
  } = useBooking();

  const onCloseBookingFlow = useCallback(() => {
    setBookingStep(null);
  }, [setBookingStep]);

  const onComplete = useCallback(
    (event: string) => {
      setDocusignUrl('');
      router.push(
        `/nantucket-rentals/request-to-book/approval?event=${event}&propertyId=${details.listing_id}`,
      );
    },
    [router, setDocusignUrl, details?.listing_id],
  );

  return (
    <>
      {children}{' '}
      {!!bookingStep && (
        <ResponsiveDialog
          open
          onOpenChange={onCloseBookingFlow}
          dialogClassName='p-0 md:h-auto md:w-[480px]'
          drawerClassName='overflow-y-hidden'
          drawerContentClassName='!p-0'
          hideCloseButton
        >
          <BookingFlowWrapper propertyDetails={details} />
        </ResponsiveDialog>
      )}
      {progressStatus === ProgressStatus.LOADING && !docusignUrl && (
        <ProgressDialog
          open={progressStatus === ProgressStatus.LOADING && !docusignUrl}
          title={'Preparing Rental Agreement ...'}
        />
      )}
      {progressStatus === ProgressStatus.SUCCESSFUL && docusignUrl && docusignUrl?.length > 0 && (
        <DocusignDialog agreementUrl={docusignUrl} onComplete={onComplete} />
      )}
    </>
  );
};

export default ListingDetailsWrapper;
