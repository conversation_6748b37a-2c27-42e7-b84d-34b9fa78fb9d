'use client';

import { memo, useCallback, useMemo } from 'react';

import { MinusIcon, PlusIcon } from '@heroicons/react/24/outline';

import FormHelperText from '@/app/ui/form-helper-text';
import InputLabel from '@/app/ui/input-label';
import { PetType } from '@/clients/contexts/BookingContext';
import { Input } from '@/clients/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/clients/ui/select';
import { Separator } from '@/clients/ui/separator';

import classNames from 'classnames';

import { GuestsValues } from '.';

type Props = {
  petsAllowed?: boolean;
  capacity?: number;
  guests: GuestsValues;
  setGuests: (guests: GuestsValues) => void;
  petCount: number;
  setPetCount: (count: number) => void;
  petType: PetType;
  setPetType: (type: PetType) => void;
  isPetSelected: boolean;
  setIsPetSelected: (selected: boolean) => void;
  petDescription: string;
  setPetDescription: (desc: string) => void;
};

const GuestSelectorComponent = ({
  petsAllowed,
  capacity,
  guests,
  setGuests,
  petCount,
  setPetCount,
  petType,
  setPetType,
  petDescription,
  setPetDescription,
  isPetSelected,
  setIsPetSelected,
}: Props) => {
  const showGuestError = useMemo(
    () => !!(capacity && guests.adults + guests.children > capacity),
    [capacity, guests.adults, guests.children],
  );

  const handleIncrement = useCallback(
    (type: 'adults' | 'children' | 'pets') => {
      if (type === 'adults' || type === 'children') {
        const newCount = guests[type] + 1;
        if (!capacity || newCount <= capacity) {
          setGuests({
            ...guests,
            [type]: newCount,
          });
        }
      } else if (type === 'pets') {
        setPetCount(petCount + 1);
      }
    },
    [capacity, guests, petCount, setGuests, setPetCount],
  );

  const handleDecrement = useCallback(
    (type: 'adults' | 'children' | 'pets') => {
      if (type === 'adults' && guests.adults > 0) {
        setGuests({
          ...guests,
          adults: guests.adults - 1,
        });
      } else if (type === 'children' && guests.children > 0) {
        setGuests({
          ...guests,
          children: guests.children - 1,
        });
      } else if (type === 'pets' && petCount > 0) {
        setPetCount(petCount - 1);
      }
    },
    [guests, petCount, setGuests, setPetCount],
  );

  const changePetDesc = useCallback(
    (event: any) => {
      const { value } = event.target;

      setPetDescription(value);
    },
    [setPetDescription],
  );

  const handlePetChange = useCallback(
    (value: string) => {
      setIsPetSelected(value === 'yes');
    },
    [setIsPetSelected],
  );

  return (
    <>
      <Separator className='my-4' />
      <div className='flex items-center justify-between text-sm text-black mb-2'>
        Adults
        <div className='flex items-center justify-between text-metal-gray font-medium w-[126px]'>
          <span
            className={classNames(
              'h-8 w-8 flex items-center justify-center rounded-full border border-solid border-platinium cursor-pointer',
              {
                '!cursor-no-drop text-disabled': guests.adults === 1,
              },
            )}
            role='button'
            onClick={() => handleDecrement('adults')}
            onKeyDown={() => handleDecrement('adults')}
            tabIndex={0}
            aria-label='Decrement Adults'
          >
            <MinusIcon className='w-4 h-4' />
          </span>
          <span className='w-5 text-center'>{guests.adults}</span>
          <span
            className={classNames(
              'h-8 w-8 flex items-center justify-center rounded-full border border-solid border-platinium cursor-pointer',
              {
                '!cursor-no-drop text-disabled': guests.adults === capacity,
              },
            )}
            role='button'
            onClick={() => handleIncrement('adults')}
            onKeyDown={() => handleIncrement('adults')}
            tabIndex={0}
            aria-label='Increment Adults'
          >
            <PlusIcon className='w-4 h-4' />
          </span>
        </div>
      </div>
      <div className='flex items-center justify-between text-sm text-black'>
        Children ( Ages 0-17 )
        <div className='flex items-center justify-between text-metal-gray font-medium w-[126px]'>
          <span
            className={classNames(
              'h-8 w-8 flex items-center justify-center rounded-full border border-solid border-platinium cursor-pointer',
              {
                '!cursor-no-drop text-disabled': guests.children === 0,
              },
            )}
            role='button'
            onClick={() => handleDecrement('children')}
            onKeyDown={() => handleDecrement('children')}
            tabIndex={0}
            aria-label='Decrement Children'
          >
            <MinusIcon className='w-4 h-4' />
          </span>
          <span className='w-5 text-center'>{guests.children}</span>
          <span
            className={classNames(
              'h-8 w-8 flex items-center justify-center rounded-full border border-solid border-platinium cursor-pointer',
              {
                '!cursor-no-drop text-disabled': guests.children === capacity,
              },
            )}
            role='button'
            onClick={() => handleIncrement('children')}
            onKeyDown={() => handleIncrement('children')}
            tabIndex={0}
            aria-label='Increment Children'
          >
            <PlusIcon className='w-4 h-4' />
          </span>
        </div>
      </div>
      {petsAllowed && (
        <div className='flex items-center justify-between text-sm text-black mt-2'>
          Bringing a pet?
          <Select value={isPetSelected ? 'yes' : 'no'} onValueChange={handlePetChange}>
            <SelectTrigger className='w-[126px] rounded-[40px]'>
              <SelectValue placeholder='' />
            </SelectTrigger>
            <SelectContent className='z-[9999]'>
              <SelectItem value='yes'>Yes</SelectItem>
              <SelectItem value='no'>No</SelectItem>
            </SelectContent>
          </Select>
        </div>
      )}
      {isPetSelected && (
        <>
          <div className='flex items-center justify-between text-sm text-black my-4'>
            <Select value={petType} onValueChange={setPetType}>
              <SelectTrigger className='w-[126px] rounded-[40px]'>
                <SelectValue placeholder='' />
              </SelectTrigger>
              <SelectContent className='z-[9999]'>
                <SelectItem value={PetType.DOG}>Dog</SelectItem>
                <SelectItem value={PetType.CAT}>Cat</SelectItem>
                <SelectItem value={PetType.OTHER}>Other</SelectItem>
              </SelectContent>
            </Select>
            <div className='flex items-center justify-between text-metal-gray font-medium w-[126px]'>
              <span
                role='button'
                onClick={() => handleDecrement('pets')}
                onKeyDown={() => handleDecrement('pets')}
                tabIndex={0}
                className='h-8 w-8 flex items-center justify-center rounded-full border border-solid border-platinium cursor-pointer'
                aria-label='Decrement Pets'
              >
                <MinusIcon className='w-4 h-4' />
              </span>
              <span className='w-5 text-center'>{petCount}</span>
              <span
                role='button'
                onClick={() => handleIncrement('pets')}
                onKeyDown={() => handleIncrement('pets')}
                tabIndex={0}
                className='h-8 w-8 flex items-center justify-center rounded-full border border-solid border-platinium cursor-pointer'
                aria-label='Increment Pets'
              >
                <PlusIcon className='w-4 h-4' />
              </span>
            </div>
          </div>
          <InputLabel className='px-[14px]'>ADDITIONAL INFORMATION (BREED, WEIGHT, ETC)</InputLabel>
          <Input
            value={petDescription}
            onChange={changePetDesc}
            className='text-black text-sm p-2.5 px-3.5 rounded-[40px]'
          />
        </>
      )}
      {showGuestError && (
        <FormHelperText className='pl-2.5' error>
          Maximum guests for this property is {capacity}
        </FormHelperText>
      )}
    </>
  );
};

export default memo(GuestSelectorComponent);
