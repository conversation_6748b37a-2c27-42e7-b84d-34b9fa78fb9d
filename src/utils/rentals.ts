import { Nullable } from '@/types/common';
import { Rent } from '@/types/rental-listing-details';

export const calculateOwnerFee = (rent: Rent, petSelected?: boolean) => {
  return rent.cleaning_fee + (petSelected ? rent.pet_fee : 0);
};

export const calculateStateAndLocalTaxes = (rent: Rent) => {
  return 0.117 * rent.rent + (rent.charge_community_impact_fee ? 0.03 * rent.rent : 0);
};

export const calculateTotalBeforeTaxes = (rent: Rent, petSelected?: boolean) => {
  return (
    rent.rent +
    0.1 * rent.rent +
    calculateOwnerFee(rent, petSelected) +
    calculateStateAndLocalTaxes(rent) -
    rent?.discount
  );
};

export const formatBookingFeesData = (
  rentInfo: Nullable<Rent>,
  isPetSelected: boolean,
  nights: number,
) => {
  const rent = Number(rentInfo?.rent ?? 0);
  const averageNightlyRate = Number(((rentInfo?.rent ?? 0) + (rentInfo?.discount ?? 0)) / nights);
  const pet_fee = isPetSelected ? Number(rentInfo?.pet_fee ?? 0) : 0;
  const other_fee = Number(rentInfo?.cleaning_fee ?? 0);
  const nantucketRentalsFee = Number(rentInfo?.fee ?? 0); // Nantucket Rentals Fee = 10% of Rent
  // if charge_community_impact_fee is true then additional 3%(ie. 14.7%) tax will be applied along with existing 11.7%
  const occupancyTaxPercentage = rentInfo?.charge_community_impact_fee ? 0.147 : 0.117;
  const ownerFee = other_fee + pet_fee;
  const occupancyTax =
    rentInfo?.tax === 0 ? 0 : (rent + nantucketRentalsFee + ownerFee) * occupancyTaxPercentage; // Occupancy Tax = 11.7% of (Rent + Nantucket Rentals Fee + Other Fees)

  const totalBeforeDiscount =
    averageNightlyRate * nights + nantucketRentalsFee + ownerFee + occupancyTax;
  const totalWithoutTaxes = rent + nantucketRentalsFee + ownerFee;
  const grandTotal = totalWithoutTaxes + occupancyTax;

  return {
    occupancyTax,
    totalWithoutTaxes,
    grandTotal,
    nantucketRentalsFee,
    averageNightlyRate,
    totalBeforeDiscount,
    other_fee,
  };
};
