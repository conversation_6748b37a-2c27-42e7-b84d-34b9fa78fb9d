import { DateRange as RDP_DateRange } from 'react-day-picker';

import { Nullable } from '@/types/common';
import { IListingAvailability, IListingRate, Rent } from '@/types/rental-listing-details';

import {
  addDays,
  addMonths,
  addYears,
  differenceInDays,
  differenceInMonths,
  endOfMonth,
  format,
  isAfter,
  isBefore,
  isSameDay,
  isSameMonth,
  isValid,
  isWithinInterval,
  parse,
  startOfDay,
  startOfMonth,
  subDays,
  subMonths,
} from 'date-fns';
import dayjs from 'dayjs';

import { getMonthsBetweenDates, MonthList } from './calendar';
import { parseDateString } from './common';

export const DAY_NAMES = [
  'Sunday',
  'Monday',
  'Tuesday',
  'Wednesday',
  'Thursday',
  'Friday',
  'Saturday',
];

export const isBetweenTwoDates = (startDate: string, endDate: string, date: Date): boolean => {
  const start = parse(startDate, 'yyyy-MM-dd', new Date());
  const end = parse(endDate, 'yyyy-MM-dd', new Date());

  return (
    isWithinInterval(date, {
      start: start,
      end: end,
    }) ||
    isSameDay(date, start) ||
    isSameDay(date, end)
  );
};

export const checkIfDateCannotBeCheckedIn = (
  date: Date,
  checkinDay: Nullable<string>,
  rentalData?: IListingRate,
  rentalDataForStartDate?: IListingRate,
) => {
  // console.log('check in day', date, checkinDay);
  if (
    !rentalData ||
    (!rentalDataForStartDate && !rentalData.allow_checkin) ||
    (rentalDataForStartDate &&
      (!rentalData.allow_checkout || (checkinDay && DAY_NAMES[date.getDay()] !== checkinDay)))
  ) {
    return true;
  }

  if (
    rentalDataForStartDate &&
    rentalDataForStartDate.minimum_nights_stay &&
    isAfter(new Date(date), parseDateString(rentalDataForStartDate.from_date)) &&
    differenceInDays(
      parseDateString(rentalData.from_date),
      parseDateString(rentalDataForStartDate.to_date),
    ) < rentalDataForStartDate.minimum_nights_stay
  ) {
    return true;
  }

  return false;
};

export const checkIfDateDisabledDesktop = (
  checkinDay: Nullable<string>,
  firstCheckinDt: Nullable<Date>,
  lastCheckinDt: Nullable<Date>,
  selectedRangeValue?: RDP_DateRange,
  rentalData?: IListingRate,
  availabilityData: IListingAvailability[] = [],
): boolean => {
  if (!rentalData) {
    return true;
  }

  if (
    !selectedRangeValue?.from &&
    ((firstCheckinDt && isBefore(parseDateString(rentalData.from_date), firstCheckinDt)) ||
      (lastCheckinDt && isAfter(parseDateString(rentalData.from_date), lastCheckinDt)))
  ) {
    return false;
  }

  if (!checkinDay && !rentalData?.allow_checkin && !selectedRangeValue?.from) {
    return true;
  }

  if (selectedRangeValue?.from) {
    if (
      (!rentalData.allow_checkout && !checkinDay) ||
      isBefore(parseDateString(rentalData.from_date), selectedRangeValue?.from) ||
      availabilityData.some((_range) =>
        rangesOverlap(
          {
            from: parseDateString(_range.from_date),
            to: parseDateString(_range.to_date),
          },
          {
            from: selectedRangeValue?.from,
            to: parseDateString(rentalData.from_date),
          },
        ),
      )
    ) {
      return true;
    }

    return false;
  }

  return false;
};

export const rangesOverlap = (range1: RDP_DateRange, range2: RDP_DateRange): boolean => {
  if (!range1?.from || !range1?.to || !range2?.from || !range2?.to) {
    return false;
  }
  return (
    isBefore(startOfDay(range1?.from), startOfDay(range2?.to)) &&
    isBefore(startOfDay(range2?.from), startOfDay(range1?.to))
  );
};

export const formatDateRangePickerRentalRates = (rentalRates: IListingRate[]) =>
  [...Array(24).keys()].reduce(
    (acc, num) => {
      const currentDt = addMonths(startOfMonth(startOfDay(new Date())), num);
      return {
        ...acc,
        [format(currentDt, 'MMM-yyyy')]: rentalRates.filter((_r) =>
          isSameMonth(currentDt, parseDateString(_r.from_date)),
        ),
      };
    },
    {} as { [_s: string]: IListingRate[] },
  );

export const getBlockedRangesForDate = (
  availabilityRanges: IListingAvailability[] = [],
  date: Date,
) => availabilityRanges.filter((_r) => isBetweenTwoDates(_r.from_date, _r.to_date, date));

export const getBlockedStartAndEndDates = (availabilityRanges: IListingAvailability[] = []) => {
  const start: Date[] = [];
  const end: Date[] = [];

  availabilityRanges.map((_r) => {
    const { from_date, to_date } = _r;
    start.push(parseDateString(from_date));
    end.push(parseDateString(to_date));
  });

  return {
    start,
    end,
  };
};

export const getCheckinDayRelativeToADate = (
  rates: IListingRate[],
  date: Date,
  availabilityData: IListingAvailability[],
) => {
  if (isBefore(startOfDay(date), startOfDay(new Date()))) {
    return null;
  }

  const checkinDays = rates?.filter(
    (_r) => _r.allow_checkin && !isBefore(parseDateString(_r.from_date), startOfDay(new Date())),
  );

  if (
    checkinDays.every(
      (_d) =>
        parseDateString(_d.from_date).getDay() ===
        parseDateString(checkinDays[0].from_date).getDay(),
    )
  ) {
    return DAY_NAMES[parseDateString(checkinDays[0]?.from_date ?? '').getDay()];
  }

  return null;
};

export const isForcedCheckinDay = (date: Date, rentalRates: IListingRate[]) => {
  if (isBefore(startOfDay(date), startOfDay(new Date()))) {
    return false;
  }

  const checkinDays = rentalRates?.filter(
    (_r) => _r.allow_checkin && !isBefore(parseDateString(_r.from_date), startOfDay(new Date())),
  );

  return checkinDays.every(
    (_d) =>
      parseDateString(_d.from_date).getDay() === parseDateString(checkinDays[0].from_date).getDay(),
  );
};

export const getStartAndEndForcedCheckinDay = (rentalRates: IListingRate[]) => {
  const checkinDays = rentalRates?.filter(
    (_r) => _r.allow_checkin && !isBefore(parseDateString(_r.from_date), startOfDay(new Date())),
  );
  const firstCheckinDay = checkinDays[0];
  const lastCheckinDay = checkinDays[checkinDays.length - 1];

  return checkinDays.every(
    (_d) =>
      parseDateString(_d.from_date).getDay() === parseDateString(checkinDays[0].from_date).getDay(),
  ) &&
    firstCheckinDay &&
    lastCheckinDay
    ? [parseDateString(firstCheckinDay.from_date), parseDateString(lastCheckinDay.from_date)]
    : [null, null];
};

export const isDateRangeSelected = (range?: RDP_DateRange) =>
  range?.from && range?.to && isValid(range.from) && isValid(range.to);

// export const formatBookingFeesData = (
//   rentInfo: Rent,
//   pet_fee = 0,
//   nights: number,
// ) => {
//   const rent = Number(rentInfo?.rent ?? 0);
//   const averageNightlyRate = Number(
//     ((rentInfo?.rent ?? 0) + (rentInfo?.discount ?? 0)) /
//       nights,
//   );
//   const otherFees = Number(rentInfo?.other_fees ?? 0);
//   const nantucketRentalsFee = Number(rentInfo?.nantucket_fee ?? 0); // Nantucket Rentals Fee = 10% of Rent
//   // if charge_community_impact_fee is true then additional 3%(ie. 14.7%) tax will be applied along with existing 11.7%
//   const occupancyTaxPercentage = rentInfo?.charge_community_impact_fee
//     ? 0.147
//     : 0.117;
//   const occupancyTax =
//     rentInfo?.occupancy_tax === 0
//       ? 0
//       : (rent + nantucketRentalsFee + otherFees + pet_fee) * occupancyTaxPercentage; // Occupancy Tax = 11.7% of (Rent + Nantucket Rentals Fee + Other Fees)
//   const totalWithoutTaxes = rent + nantucketRentalsFee + otherFees + pet_fee;
//   const grandTotal = totalWithoutTaxes + occupancyTax;
//   const totalBeforeDiscount = averageNightlyRate * nights + nantucketRentalsFee + otherFees;
//   return {
//     occupancyTax,
//     totalWithoutTaxes,
//     grandTotal,
//     nantucketRentalsFee,
//     averageNightlyRate,
//     totalBeforeDiscount,
//   };
// };

export const getDateRangePickerMonths = () => {
  const months: MonthList = [];
  const minDate = startOfMonth(new Date());
  const maxDate = endOfMonth(subMonths(addYears(new Date(), 2), 1));
  const numberOfMonths = differenceInMonths(maxDate, minDate);
  for (let i = 0; i <= numberOfMonths; i++) {
    months.push({
      year: addMonths(minDate, i).getFullYear(),
      month: addMonths(minDate, i).getMonth(),
    });
  }
  return months;
};

export const getMonthYearsList = (year?: number): MonthList => {
  const currentMonth = dayjs(year && `${year}`, `YYYY`).startOf('year');
  return getMonthsBetweenDates(
    currentMonth.format('YYYY-MM-DD'),
    currentMonth.add(11, 'months').format('YYYY-MM-DD'),
  );
};

export const getAvailabilityDataForCurrentMonth = (
  availabilityData: IListingAvailability[],
  currentMonth: number,
  currentYear: number,
) => {
  // Create a proper date object for the first day of the month
  // currentMonth is expected to be 1-based (1-12), so convert to 0-based for JavaScript Date
  const monthIndex = currentMonth - 1;
  const dateObject = new Date(currentYear, monthIndex, 1);

  // Define the range to check: from the last day of previous month to 7 days into next month
  const minDate = subDays(dateObject, 1);
  const maxDate = addDays(startOfMonth(addMonths(dateObject, 1)), 7);

  return availabilityData.filter((_a) => {
    const fromDate = parseDateString(_a.from_date);
    const toDate = parseDateString(_a.to_date);

    // Check if availability range overlaps with our target month range
    return (
      // Availability ends within our range
      isWithinInterval(toDate, { start: minDate, end: maxDate }) ||
      // Availability starts within our range
      isWithinInterval(fromDate, { start: minDate, end: maxDate }) ||
      // Availability completely encompasses our range
      (isBefore(fromDate, minDate) && isAfter(toDate, maxDate))
    );
  });
};

export const checkIfDateHasRate = (date: Date, rentalRates: IListingRate[] = []): boolean => {
  return (
    rentalRates.filter(({ from_date, to_date }) => isBetweenTwoDates(from_date, to_date, date))
      .length > 0
  );
};
